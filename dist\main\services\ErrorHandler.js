"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandler = void 0;
const events_1 = require("events");
const electron_1 = require("electron");
class ErrorHandler extends events_1.EventEmitter {
    constructor() {
        super();
        Object.defineProperty(this, "errorLog", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: []
        });
        Object.defineProperty(this, "maxLogSize", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: 100
        });
    }
    handleError(message, error, severity = 'medium') {
        const errorInfo = {
            message,
            details: error,
            timestamp: new Date(),
            severity,
            category: this.categorizeError(message, error)
        };
        // 添加到錯誤日誌
        this.addToLog(errorInfo);
        // 記錄到控制台
        this.logToConsole(errorInfo);
        // 發送通知（根據嚴重程度）
        if (severity === 'high' || severity === 'critical') {
            this.showNotification(errorInfo);
        }
        // 發出錯誤事件
        this.emit('error-occurred', errorInfo);
        // 對於嚴重錯誤，可能需要特殊處理
        if (severity === 'critical') {
            this.handleCriticalError(errorInfo);
        }
    }
    categorizeError(message, _error) {
        const lowerMessage = message.toLowerCase();
        if (lowerMessage.includes('audio') || lowerMessage.includes('recording') || lowerMessage.includes('microphone')) {
            return 'audio';
        }
        else if (lowerMessage.includes('api') || lowerMessage.includes('azure') || lowerMessage.includes('openai')) {
            return 'api';
        }
        else if (lowerMessage.includes('network') || lowerMessage.includes('timeout') || lowerMessage.includes('connection')) {
            return 'network';
        }
        else if (lowerMessage.includes('config') || lowerMessage.includes('setting') || lowerMessage.includes('key')) {
            return 'config';
        }
        else if (lowerMessage.includes('system') || lowerMessage.includes('permission') || lowerMessage.includes('hotkey')) {
            return 'system';
        }
        else {
            return 'unknown';
        }
    }
    addToLog(errorInfo) {
        this.errorLog.unshift(errorInfo);
        // 保持日誌大小限制
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(0, this.maxLogSize);
        }
    }
    logToConsole(errorInfo) {
        const timestamp = errorInfo.timestamp.toISOString();
        const prefix = `[${timestamp}] [${errorInfo.severity.toUpperCase()}] [${errorInfo.category.toUpperCase()}]`;
        switch (errorInfo.severity) {
            case 'low':
                console.info(`${prefix} ${errorInfo.message}`, errorInfo.details);
                break;
            case 'medium':
                console.warn(`${prefix} ${errorInfo.message}`, errorInfo.details);
                break;
            case 'high':
            case 'critical':
                console.error(`${prefix} ${errorInfo.message}`, errorInfo.details);
                break;
        }
    }
    showNotification(errorInfo) {
        try {
            const notification = new electron_1.Notification({
                title: 'SpeechPilot 錯誤',
                body: this.getUserFriendlyMessage(errorInfo),
                icon: undefined, // 可以添加錯誤圖標
                urgency: errorInfo.severity === 'critical' ? 'critical' : 'normal'
            });
            notification.show();
            notification.on('click', () => {
                this.emit('notification-clicked', errorInfo);
            });
        }
        catch (error) {
            console.error('Failed to show notification:', error);
        }
    }
    getUserFriendlyMessage(errorInfo) {
        const { message, category } = errorInfo;
        // 根據錯誤類別提供用戶友好的訊息
        switch (category) {
            case 'audio':
                if (message.includes('permission')) {
                    return '麥克風權限被拒絕，請檢查系統設定';
                }
                else if (message.includes('device')) {
                    return '找不到麥克風設備，請檢查設備連接';
                }
                else {
                    return '音頻錄製出現問題，請重試';
                }
            case 'api':
                if (message.includes('key')) {
                    return 'API 金鑰無效，請檢查設定';
                }
                else if (message.includes('quota') || message.includes('limit')) {
                    return 'API 使用額度不足，請稍後再試';
                }
                else {
                    return 'AI 服務暫時不可用，請稍後再試';
                }
            case 'network':
                return '網路連接有問題，請檢查網路設定';
            case 'config':
                return '配置設定有誤，請檢查設定面板';
            case 'system':
                if (message.includes('hotkey')) {
                    return '快捷鍵註冊失敗，請嘗試其他組合';
                }
                else {
                    return '系統權限不足，請以管理員身份運行';
                }
            default:
                return '發生未知錯誤，請重試或聯繫支援';
        }
    }
    handleCriticalError(errorInfo) {
        console.error('Critical error occurred:', errorInfo);
        // 對於嚴重錯誤，可能需要：
        // 1. 保存當前狀態
        // 2. 重置服務
        // 3. 通知用戶
        // 4. 發送錯誤報告（如果用戶同意）
        this.emit('critical-error', errorInfo);
    }
    // 獲取錯誤統計
    getErrorStats() {
        const byCategory = {
            audio: 0,
            api: 0,
            system: 0,
            network: 0,
            config: 0,
            unknown: 0
        };
        const bySeverity = {
            low: 0,
            medium: 0,
            high: 0,
            critical: 0
        };
        this.errorLog.forEach(error => {
            byCategory[error.category]++;
            bySeverity[error.severity]++;
        });
        return {
            total: this.errorLog.length,
            byCategory,
            bySeverity,
            recent: this.errorLog.slice(0, 10) // 最近 10 個錯誤
        };
    }
    // 獲取特定類別的錯誤
    getErrorsByCategory(category) {
        return this.errorLog.filter(error => error.category === category);
    }
    // 獲取特定嚴重程度的錯誤
    getErrorsBySeverity(severity) {
        return this.errorLog.filter(error => error.severity === severity);
    }
    // 清除錯誤日誌
    clearErrorLog() {
        this.errorLog = [];
        this.emit('error-log-cleared');
    }
    // 導出錯誤日誌
    exportErrorLog() {
        return JSON.stringify(this.errorLog, null, 2);
    }
    // 檢查是否有重複錯誤
    hasDuplicateErrors(timeWindow = 60000) {
        const now = Date.now();
        const recentErrors = this.errorLog.filter(error => now - error.timestamp.getTime() < timeWindow);
        const errorMessages = recentErrors.map(error => error.message);
        const uniqueMessages = new Set(errorMessages);
        return errorMessages.length > uniqueMessages.size;
    }
    // 獲取錯誤趨勢
    getErrorTrend(hours = 24) {
        const now = new Date();
        const trend = [];
        for (let i = 0; i < hours; i++) {
            const hourStart = new Date(now.getTime() - (i + 1) * 60 * 60 * 1000);
            const hourEnd = new Date(now.getTime() - i * 60 * 60 * 1000);
            const count = this.errorLog.filter(error => error.timestamp >= hourStart && error.timestamp < hourEnd).length;
            trend.unshift({ hour: hourStart.getHours(), count });
        }
        return trend;
    }
    // 建議解決方案
    getSuggestions(errorInfo) {
        const suggestions = [];
        switch (errorInfo.category) {
            case 'audio':
                suggestions.push('檢查麥克風是否正確連接');
                suggestions.push('確認麥克風權限已開啟');
                suggestions.push('嘗試重新啟動應用程式');
                break;
            case 'api':
                suggestions.push('檢查 API 金鑰是否正確');
                suggestions.push('確認網路連接正常');
                suggestions.push('檢查 API 使用額度');
                break;
            case 'network':
                suggestions.push('檢查網路連接');
                suggestions.push('嘗試使用其他網路');
                suggestions.push('檢查防火牆設定');
                break;
            case 'config':
                suggestions.push('重新檢查設定面板');
                suggestions.push('嘗試重置為預設設定');
                suggestions.push('重新啟動應用程式');
                break;
            case 'system':
                suggestions.push('以管理員身份運行應用程式');
                suggestions.push('檢查系統權限設定');
                suggestions.push('嘗試不同的快捷鍵組合');
                break;
            default:
                suggestions.push('重新啟動應用程式');
                suggestions.push('檢查系統資源使用情況');
                suggestions.push('聯繫技術支援');
                break;
        }
        return suggestions;
    }
    // 清理資源
    cleanup() {
        this.clearErrorLog();
        this.removeAllListeners();
    }
}
exports.ErrorHandler = ErrorHandler;
