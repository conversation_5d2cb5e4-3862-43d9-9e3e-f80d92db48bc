"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AzureOpenAIService = void 0;
const axios_1 = __importDefault(require("axios"));
const events_1 = require("events");
const constants_1 = require("../../shared/constants");
class AzureOpenAIService extends events_1.EventEmitter {
    constructor() {
        super();
        Object.defineProperty(this, "client", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "config", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "wsConnection", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
    }
    updateConfig(config) {
        this.config = config;
        this.initializeClient();
    }
    initializeClient() {
        if (!this.config?.apiKey || !this.config?.endpoint) {
            console.warn('Azure OpenAI config is incomplete');
            return;
        }
        try {
            this.client = axios_1.default.create({
                baseURL: this.config.endpoint,
                headers: {
                    'api-key': this.config.apiKey,
                    'Content-Type': 'application/json'
                },
                timeout: constants_1.API_CONFIG.timeout
            });
            console.log('Azure OpenAI Service initialized');
        }
        catch (error) {
            console.error('Failed to initialize Azure OpenAI Service:', error);
            this.client = null;
        }
    }
    async processAudioWithAI(audioBuffer) {
        if (!this.client || !this.config) {
            throw new Error(constants_1.ERROR_MESSAGES.API_KEY_MISSING);
        }
        const startTime = Date.now();
        console.log('🎯 [AI Processing] Starting audio processing with AI...');
        console.log(`📊 [AI Processing] Audio buffer size: ${audioBuffer.length} bytes`);
        console.log(`🔧 [AI Processing] Using model: ${this.config.model}`);
        try {
            // 將音頻轉換為 base64
            console.log('🔄 [AI Processing] Converting audio to base64...');
            const audioBase64 = audioBuffer.toString('base64');
            console.log(`📏 [AI Processing] Base64 size: ${audioBase64.length} characters`);
            // 構建請求
            console.log('📝 [AI Processing] Building request body...');
            const requestBody = {
                model: this.config.model,
                messages: [
                    {
                        role: 'system',
                        content: `你是一個智能語音助手。用戶會通過語音給你指令，你需要理解用戶的意圖並提供相應的回應。

規則：
1. 如果用戶要求寫作（如寫文章、寫郵件、寫報告等），請直接提供完整的內容
2. 如果用戶要求翻譯，請直接提供翻譯結果
3. 如果用戶要求解釋或回答問題，請提供清晰的答案
4. 如果用戶要求創作（如寫詩、寫故事等），請直接提供創作內容
5. 回應應該直接可用，不需要額外的說明或前綴
6. 保持回應簡潔但完整
7. 使用繁體中文回應（除非用戶特別要求其他語言）

請根據用戶的語音指令提供適當的回應。`
                    },
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'input_audio',
                                input_audio: {
                                    data: audioBase64,
                                    format: 'wav'
                                }
                            }
                        ]
                    }
                ],
                temperature: 0.7,
                max_tokens: 1000
            };
            // 發送請求
            const apiUrl = `/openai/deployments/${this.config.model}/chat/completions?api-version=2024-10-01-preview`;
            console.log(`🌐 [AI Processing] Sending request to: ${this.config.endpoint}${apiUrl}`);
            console.log(`⏰ [AI Processing] Request timeout set to: ${constants_1.API_CONFIG.timeout}ms`);
            console.log('⏳ [AI Processing] Waiting for AI response...');
            // 創建帶超時的請求
            const response = await Promise.race([
                this.client.post(apiUrl, requestBody),
                new Promise((_, reject) => setTimeout(() => reject(new Error('AI processing timeout')), constants_1.API_CONFIG.timeout))
            ]);
            const processingTime = Date.now() - startTime;
            console.log(`⚡ [AI Processing] Request completed in ${processingTime}ms`);
            console.log(`📊 [AI Processing] Response status: ${response.status}`);
            // 提取回應文本
            const result = response.data?.choices?.[0]?.message?.content;
            if (!result) {
                console.error('❌ [AI Processing] No content in response:', response.data);
                throw new Error('No response from AI model');
            }
            console.log(`✅ [AI Processing] AI processing completed successfully`);
            console.log(`📝 [AI Processing] Result preview: ${result.substring(0, 100)}...`);
            console.log(`⏱️ [AI Processing] Total processing time: ${processingTime}ms`);
            return result.trim();
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            console.error(`❌ [AI Processing] Error occurred after ${processingTime}ms:`, error);
            if (axios_1.default.isAxiosError(error)) {
                console.error(`🌐 [AI Processing] HTTP Error - Status: ${error.response?.status}`);
                console.error(`🌐 [AI Processing] HTTP Error - Data:`, error.response?.data);
                if (error.response?.status === 401) {
                    throw new Error('API 金鑰無效或已過期');
                }
                else if (error.response?.status === 429) {
                    throw new Error('API 請求頻率超限，請稍後再試');
                }
                else if (error.response?.status === 500) {
                    throw new Error('Azure OpenAI 服務暫時不可用');
                }
                else {
                    throw new Error(`API 請求失敗: ${error.response?.status} ${error.response?.statusText}`);
                }
            }
            else if (error instanceof Error) {
                console.error(`🔧 [AI Processing] Error type: ${error.constructor.name}`);
                console.error(`🔧 [AI Processing] Error message: ${error.message}`);
                if (error.message.includes('timeout')) {
                    throw new Error('請求超時，請檢查網路連接');
                }
                else {
                    throw new Error(`AI 處理失敗: ${error.message}`);
                }
            }
            else {
                console.error(`❓ [AI Processing] Unknown error type:`, error);
                throw new Error('未知的 AI 處理錯誤');
            }
        }
    }
    // 使用 gpt-4o-mini-transcribe 進行直接語音轉文字
    async transcribeAudio(audioBuffer) {
        if (!this.config) {
            throw new Error(constants_1.ERROR_MESSAGES.API_KEY_MISSING);
        }
        return new Promise((resolve, reject) => {
            try {
                // 使用 OpenAI Realtime API 進行轉錄
                const wsUrl = `wss://api.openai.com/v1/realtime?intent=transcription`;
                // Note: WebSocket headers need to be handled differently in Node.js
                this.wsConnection = new WebSocket(wsUrl);
                let transcriptionResult = '';
                let isCompleted = false;
                this.wsConnection.onopen = () => {
                    console.log('WebSocket connection opened for transcription');
                    // 設置轉錄會話
                    const sessionConfig = {
                        type: 'transcription_session.update',
                        input_audio_format: 'pcm16',
                        input_audio_transcription: {
                            model: 'gpt-4o-mini-transcribe',
                            prompt: '',
                            language: 'zh'
                        },
                        turn_detection: {
                            type: 'server_vad',
                            threshold: 0.5,
                            prefix_padding_ms: 300,
                            silence_duration_ms: 500
                        },
                        input_audio_noise_reduction: {
                            type: 'near_field'
                        }
                    };
                    this.wsConnection.send(JSON.stringify(sessionConfig));
                    // 發送音頻數據
                    const audioBase64 = audioBuffer.toString('base64');
                    const audioMessage = {
                        type: 'input_audio_buffer.append',
                        audio: audioBase64
                    };
                    this.wsConnection.send(JSON.stringify(audioMessage));
                };
                this.wsConnection.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'input_audio_transcription.completed') {
                            transcriptionResult += data.transcript || '';
                        }
                        else if (data.type === 'input_audio_buffer.committed') {
                            // 音頻緩衝區已提交，轉錄完成
                            if (!isCompleted) {
                                isCompleted = true;
                                this.wsConnection?.close();
                                resolve(transcriptionResult.trim() || '無法識別語音內容');
                            }
                        }
                        else if (data.type === 'error') {
                            reject(new Error(data.error?.message || '轉錄過程中發生錯誤'));
                        }
                    }
                    catch (parseError) {
                        console.error('Error parsing WebSocket message:', parseError);
                    }
                };
                this.wsConnection.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    reject(new Error('WebSocket 連接錯誤'));
                };
                this.wsConnection.onclose = () => {
                    console.log('WebSocket connection closed');
                    if (!isCompleted) {
                        resolve(transcriptionResult.trim() || '轉錄未完成');
                    }
                };
                // 設置超時
                setTimeout(() => {
                    if (!isCompleted) {
                        isCompleted = true;
                        this.wsConnection?.close();
                        if (transcriptionResult.trim()) {
                            resolve(transcriptionResult.trim());
                        }
                        else {
                            reject(new Error('轉錄超時'));
                        }
                    }
                }, constants_1.API_CONFIG.timeout);
            }
            catch (error) {
                console.error('Transcription error:', error);
                reject(error);
            }
        });
    }
    // 文本對話（用於測試或文本輸入）
    async chatCompletion(message) {
        if (!this.client || !this.config) {
            throw new Error(constants_1.ERROR_MESSAGES.API_KEY_MISSING);
        }
        try {
            const requestBody = {
                model: this.config.model,
                messages: [
                    {
                        role: 'system',
                        content: '你是一個智能助手，請用繁體中文回應用戶的問題。'
                    },
                    {
                        role: 'user',
                        content: message
                    }
                ],
                temperature: 0.7,
                max_tokens: 1000
            };
            const response = await this.client.post(`/openai/deployments/${this.config.model}/chat/completions?api-version=2024-10-01-preview`, requestBody);
            const result = response.data?.choices?.[0]?.message?.content;
            if (!result) {
                throw new Error('No response from AI model');
            }
            return result.trim();
        }
        catch (error) {
            console.error('Chat completion error:', error);
            throw this.handleAPIError(error);
        }
    }
    // 檢查服務可用性
    async checkServiceAvailability() {
        if (!this.client || !this.config) {
            return false;
        }
        try {
            // 發送一個簡單的測試請求
            const response = await this.client.post(`/openai/deployments/${this.config.model}/chat/completions?api-version=2024-10-01-preview`, {
                model: this.config.model,
                messages: [
                    {
                        role: 'user',
                        content: 'Hello'
                    }
                ],
                max_tokens: 5
            });
            return response.status === 200;
        }
        catch (error) {
            console.error('Service availability check failed:', error);
            return false;
        }
    }
    // 獲取模型信息
    async getModelInfo() {
        if (!this.client) {
            throw new Error(constants_1.ERROR_MESSAGES.API_KEY_MISSING);
        }
        try {
            const response = await this.client.get('/openai/models?api-version=2024-10-01-preview');
            return response.data;
        }
        catch (error) {
            console.error('Failed to get model info:', error);
            throw this.handleAPIError(error);
        }
    }
    // 設置模型參數
    setModelParameters(temperature = 0.7, maxTokens = 1000) {
        // 這些參數將在下次請求時使用
        console.log(`Model parameters updated: temperature=${temperature}, maxTokens=${maxTokens}`);
    }
    handleAPIError(error) {
        if (axios_1.default.isAxiosError(error)) {
            if (error.response?.status === 401) {
                return new Error('API 金鑰無效或已過期');
            }
            else if (error.response?.status === 429) {
                return new Error('API 請求頻率超限，請稍後再試');
            }
            else if (error.response?.status === 500) {
                return new Error('Azure OpenAI 服務暫時不可用');
            }
            else {
                return new Error(`API 請求失敗: ${error.response?.status} ${error.response?.statusText}`);
            }
        }
        else if (error instanceof Error) {
            if (error.message.includes('timeout')) {
                return new Error('請求超時，請檢查網路連接');
            }
            else {
                return new Error(`服務錯誤: ${error.message}`);
            }
        }
        else {
            return new Error('未知的服務錯誤');
        }
    }
    // 重試機制
    // TODO: Implement retry logic when needed
    // private async retryRequest<T>(...) { ... }
    // 清理資源
    cleanup() {
        if (this.wsConnection) {
            this.wsConnection.close();
            this.wsConnection = null;
        }
        this.client = null;
        this.config = null;
        this.removeAllListeners();
    }
}
exports.AzureOpenAIService = AzureOpenAIService;
