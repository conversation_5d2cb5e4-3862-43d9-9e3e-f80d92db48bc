"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// SpeechPilot - 重構版本
const electron_1 = require("electron");
const fs_1 = require("fs");
const path_1 = require("path");
const dotenv_1 = require("dotenv");
const os_1 = require("os");
// 服務導入
const AudioService_1 = require("./services/AudioService");
const AIService_1 = require("./services/AIService");
const TextInputService_1 = require("./services/TextInputService");
const RecordingWindow_1 = require("./windows/RecordingWindow");
const SettingsWindow_1 = require("./windows/SettingsWindow");
// 載入環境變數
const envPath = (0, path_1.join)(process.cwd(), '.env.local');
(0, dotenv_1.config)({ path: envPath });
// 禁用 GPU 加速
electron_1.app.disableHardwareAcceleration();
class SpeechPilot {
    constructor() {
        this.isRecording = false;
        this.tray = null;
        this.recordingProcess = null;
        this.tempAudioFile = (0, path_1.join)(__dirname, 'temp_audio.wav');
        // 服務實例
        this.audioService = new AudioService_1.AudioService();
        this.textInputService = new TextInputService_1.TextInputService();
        this.aiService = new AIService_1.AIService(this.textInputService);
        this.recordingWindow = new RecordingWindow_1.RecordingWindow();
        this.settingsWindow = new SettingsWindow_1.SettingsWindow();
    }
    async init() {
        console.log('🚀 Starting SpeechPilot - Refactored Version...');
        console.log(`💻 Platform: ${(0, os_1.platform)()}`);
        // 檢查環境變數
        if (!process.env.AZURE_OPENAI_API_KEY || !process.env.AZURE_OPENAI_ENDPOINT) {
            console.error('❌ Missing Azure OpenAI configuration');
            return;
        }
        console.log('✅ Azure OpenAI configured:', process.env.AZURE_OPENAI_ENDPOINT);
        console.log('✅ Model:', process.env.AZURE_OPENAI_MODEL);
        // 設置系統托盤
        this.setupTray();
        // 註冊快捷鍵
        this.setupHotkeys();
        // 設置 IPC 處理器
        this.setupIPC();
        console.log('✅ SpeechPilot ready!');
    }
    setupTray() {
        const icon = electron_1.nativeImage.createEmpty();
        this.tray = new electron_1.Tray(icon);
        const contextMenu = electron_1.Menu.buildFromTemplate([
            { label: '⚙️ 設置', click: () => this.settingsWindow.show() },
            { type: 'separator' },
            { label: '❌ 退出', click: () => electron_1.app.quit() }
        ]);
        this.tray.setContextMenu(contextMenu);
        this.tray.setToolTip('SpeechPilot - AI 語音助手');
    }
    setupHotkeys() {
        electron_1.globalShortcut.unregisterAll();
        const aiRegistered = electron_1.globalShortcut.register('CommandOrControl+Shift+C', () => {
            console.log('🎯 AI hotkey triggered!');
            this.toggleRecording('ai');
        });
        const directRegistered = electron_1.globalShortcut.register('CommandOrControl+Shift+V', () => {
            console.log('🎯 Direct hotkey triggered!');
            this.toggleRecording('direct');
        });
        console.log(`🎹 Hotkeys: AI=${aiRegistered ? '✅' : '❌'}, Direct=${directRegistered ? '✅' : '❌'}`);
    }
    setupIPC() {
        // 處理窗口移動
        electron_1.ipcMain.on('move-window', (event, { deltaX, deltaY }) => {
            const window = this.recordingWindow;
            if (window.isVisible()) {
                // 這裡可以添加窗口移動邏輯
            }
        });
        // 處理錄音窗口關閉事件
        electron_1.ipcMain.on('recording-window-closed', () => {
            console.log('🪟 Recording window closed by user, stopping recording...');
            if (this.isRecording) {
                this.stopRecording();
            }
        });
    }
    toggleRecording(mode) {
        console.log(`🔄 Toggle recording - Current state: ${this.isRecording ? 'Recording' : 'Stopped'}`);
        if (this.isRecording) {
            // 如果正在錄音，停止錄音
            console.log('⏹️ Stopping recording...');
            this.stopRecording();
        }
        else {
            // 如果沒有錄音，開始錄音
            console.log(`🎙️ Starting ${mode} recording...`);
            this.startRecording(mode);
        }
    }
    async startRecording(mode) {
        this.isRecording = true;
        console.log(`🎙️ Starting ${mode} mode - press hotkey again to stop...`);
        // 創建並顯示錄音窗口
        this.recordingWindow.create();
        this.recordingWindow.updateStatus({
            isRecording: true,
            mode: mode,
            status: 'recording'
        });
        try {
            // 直接錄音到內存，不保存文件
            await this.startDirectAudioCapture(mode);
        }
        catch (error) {
            console.error('❌ Failed to start recording:', error);
            this.isRecording = false;
            this.recordingWindow.updateStatus({
                isRecording: false,
                status: 'error',
                message: '錄音啟動失敗'
            });
            this.recordingWindow.close(2000);
        }
    }
    stopRecording() {
        console.log('⏹️ User requested to stop recording...');
        if (this.recordingProcess) {
            // 發送 SIGINT 信號給 FFmpeg 進行優雅停止
            this.recordingProcess.kill('SIGINT');
            console.log('📝 Stop signal sent to FFmpeg');
        }
        else {
            // 如果是實時串流模式，停止 WebSocket 連接
            this.aiService.stopRealtimeTranscription();
            this.isRecording = false;
            // 更新錄音窗口狀態並關閉
            this.recordingWindow.updateStatus({
                isRecording: false,
                status: 'completed',
                message: '錄音已停止'
            });
            this.recordingWindow.close(1000);
            console.log('⚠️ Stopped realtime transcription and closed window');
        }
        console.log('✅ Recording stop requested');
    }
    async startDirectAudioCapture(mode) {
        console.log(`🎤 Starting direct audio capture for ${mode} mode...`);
        console.log('🔧 Using bundled FFmpeg for high-quality audio capture...');
        // 重置文字輸入狀態，清除上一輪的緩存
        this.textInputService.resetStreamingState();
        console.log('🔄 Reset text input state for new recording');
        // 獲取音頻設備
        const audioDevice = await this.audioService.getSelectedAudioDevice();
        if (!audioDevice) {
            console.error('❌ No audio input device found');
            this.isRecording = false;
            this.recordingWindow.updateStatus({
                isRecording: false,
                status: 'error',
                message: '未找到音頻輸入設備'
            });
            this.recordingWindow.close(2000);
            return;
        }
        if (mode === 'direct') {
            // 直接轉錄模式：立即開始實時 WebSocket 串流
            console.log('🔄 Starting real-time WebSocket streaming...');
            await this.startRealtimeStreaming(audioDevice);
        }
        else {
            // AI 模式：錄音到內存後處理
            await this.startBufferedRecording(audioDevice, mode);
        }
    }
    async startRealtimeStreaming(audioDevice) {
        try {
            // 立即連接 WebSocket 並開始實時串流
            await this.aiService.startRealtimeTranscription(audioDevice, this.audioService, 
            // 部分轉錄回調
            (partialText) => {
                console.log('🔄 Real-time partial transcription:', partialText);
                this.recordingWindow.updateStatus({
                    status: 'processing',
                    message: `轉錄中: ${partialText.substring(0, 30)}...`
                });
            }, 
            // 最終轉錄回調
            (finalText) => {
                console.log('✅ Real-time final transcription:', finalText);
                this.recordingWindow.updateStatus({
                    status: 'completed',
                    message: '轉錄完成'
                });
            }, 
            // 錄音停止回調
            () => {
                this.isRecording = false;
                this.recordingWindow.close(1000);
            }, 
            // 音量更新回調
            (volume) => {
                this.recordingWindow.updateStatus({
                    volume: volume
                });
            });
        }
        catch (error) {
            console.error('❌ Failed to start realtime streaming:', error);
            this.isRecording = false;
            this.recordingWindow.updateStatus({
                isRecording: false,
                status: 'error',
                message: '實時串流啟動失敗'
            });
            this.recordingWindow.close(2000);
        }
    }
    async startBufferedRecording(audioDevice, mode) {
        // 用於收集音頻數據的緩衝區
        const audioChunks = [];
        // AI 模式的 5 秒超時機制
        let silenceTimer = null;
        let lastVolumeTime = Date.now();
        const SILENCE_TIMEOUT = 5000; // 5 秒
        // 使用 AudioService 創建錄音進程
        try {
            this.recordingProcess = this.audioService.createRecordingProcess(audioDevice);
        }
        catch (error) {
            console.error('❌ Failed to create recording process:', error);
            this.isRecording = false;
            this.recordingWindow.updateStatus({
                isRecording: false,
                status: 'error',
                message: 'FFmpeg 未正確打包'
            });
            this.recordingWindow.close(2000);
            return;
        }
        // 收集音頻數據並實時檢測音量
        let totalBytes = 0;
        this.recordingProcess.stdout?.on('data', (chunk) => {
            audioChunks.push(chunk);
            totalBytes += chunk.length;
            // 實時音量檢測
            if (chunk.length > 44) { // 確保有足夠的數據
                const pcmData = chunk.subarray(44); // 跳過 WAV 標頭
                if (pcmData.length > 0) {
                    const samples = new Int16Array(pcmData.buffer, pcmData.byteOffset, pcmData.length / 2);
                    const maxAmplitude = Math.max(...Array.from(samples).map(Math.abs));
                    const volume = Math.min(100, Math.round((maxAmplitude / 32767) * 100));
                    // 發送音量更新到錄音窗口
                    this.recordingWindow.updateStatus({
                        volume: volume
                    });
                    // AI 模式的靜音檢測和超時機制
                    if (mode === 'ai') {
                        if (volume > 10) { // 有聲音
                            lastVolumeTime = Date.now();
                            // 清除現有的靜音計時器
                            if (silenceTimer) {
                                clearTimeout(silenceTimer);
                                silenceTimer = null;
                            }
                        }
                        else { // 靜音
                            // 如果還沒有設置靜音計時器，設置一個
                            if (!silenceTimer) {
                                silenceTimer = setTimeout(() => {
                                    console.log('⏰ AI mode: 5 seconds of silence detected, auto-stopping...');
                                    this.recordingWindow.updateStatus({
                                        status: 'processing',
                                        message: '檢測到靜音，自動停止錄音...'
                                    });
                                    this.stopRecording();
                                }, SILENCE_TIMEOUT);
                            }
                        }
                    }
                }
            }
            // 每秒顯示一次進度 (假設每秒約 32KB 數據)
            if (totalBytes % 32000 < chunk.length) {
                const seconds = Math.floor(totalBytes / 32000);
                console.log(`🎤 Recording: ${seconds}s (${Math.round(totalBytes / 1024)}KB)`);
            }
        });
        // 監聽 FFmpeg 的錯誤輸出
        this.recordingProcess.stderr?.on('data', (data) => {
            const errorMsg = data.toString();
            // 只顯示重要的錯誤信息，過濾掉正常的進度信息
            if (!errorMsg.includes('size=') && !errorMsg.includes('time=') && !errorMsg.includes('bitrate=')) {
                console.log('🔧 FFmpeg stderr:', errorMsg.trim());
            }
        });
        this.recordingProcess.on('close', async (code) => {
            console.log(`🎙️ Recording finished with code: ${code}`);
            // 檢查是否有音頻數據，不管退出代碼如何
            if (audioChunks.length > 0) {
                // 合併所有音頻塊
                const audioBuffer = Buffer.concat(audioChunks);
                console.log(`📁 Total audio size: ${audioBuffer.length} bytes`);
                // 檢查音頻數據是否足夠（至少 1 秒的數據）
                if (audioBuffer.length > 32000) { // 約 1 秒的 16kHz 單聲道音頻
                    try {
                        // 更新窗口狀態為處理中
                        this.recordingWindow.updateStatus({
                            isRecording: false,
                            status: 'processing',
                            message: mode === 'ai' ? '正在進行 AI 處理...' : '正在轉錄音頻...'
                        });
                        let result;
                        // 只有 AI 模式會進入 startBufferedRecording
                        // 直接轉錄模式使用 startRealtimeStreaming，不會到這裡
                        result = await this.aiService.processWithAI(audioBuffer);
                        console.log('✅ AI Processing completed:', result.substring(0, 100) + '...');
                        // 更新窗口狀態為完成
                        this.recordingWindow.updateStatus({
                            status: 'completed',
                            message: '處理完成，正在輸入文字...'
                        });
                        await this.textInputService.inputText(result);
                        // 關閉窗口
                        this.recordingWindow.close(1000);
                    }
                    catch (error) {
                        console.error('❌ Processing error:', error);
                        this.recordingWindow.updateStatus({
                            status: 'error',
                            message: `處理失敗：${error.message}`
                        });
                        this.recordingWindow.close(3000);
                    }
                }
                else {
                    console.log('⚠️ Audio data too short, skipping processing');
                    this.recordingWindow.updateStatus({
                        status: 'error',
                        message: '錄音時間太短，請重試'
                    });
                    this.recordingWindow.close(2000);
                }
            }
            else {
                console.log('❌ No audio data captured');
                this.recordingWindow.updateStatus({
                    status: 'error',
                    message: '未捕獲到音頻數據，請重試'
                });
                this.recordingWindow.close(2000);
            }
            this.isRecording = false;
        });
        this.recordingProcess.on('error', (error) => {
            console.error('❌ FFmpeg recording error:', error);
            this.isRecording = false;
            this.recordingWindow.updateStatus({
                isRecording: false,
                status: 'error',
                message: '音頻錄製失敗'
            });
            this.recordingWindow.close(2000);
        });
        console.log('🎤 FFmpeg recording started - press hotkey again to stop');
    }
    destroy() {
        electron_1.globalShortcut.unregisterAll();
        if (this.recordingProcess) {
            this.recordingProcess.kill('SIGTERM');
        }
        if ((0, fs_1.existsSync)(this.tempAudioFile)) {
            (0, fs_1.unlinkSync)(this.tempAudioFile);
        }
        this.tray?.destroy();
        this.recordingWindow.close(0);
    }
}
// 應用程式啟動
let speechPilot;
electron_1.app.whenReady().then(async () => {
    speechPilot = new SpeechPilot();
    await speechPilot.init();
});
electron_1.app.on('window-all-closed', () => {
    // 保持運行
});
electron_1.app.on('before-quit', () => {
    speechPilot?.destroy();
});
// 防止多個實例
const gotTheLock = electron_1.app.requestSingleInstanceLock();
if (!gotTheLock) {
    electron_1.app.quit();
}
