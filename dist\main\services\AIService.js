"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const ws_1 = __importDefault(require("ws"));
class AIService {
    constructor(textInputService) {
        this.realtimeWebSocket = null;
        this.realtimeRecordingProcess = null;
        this.textInputService = textInputService;
    }
    async processWithAI(audioBuffer) {
        console.log('🧠 Processing with AI using Chat Completions API...');
        const audioBase64 = audioBuffer.toString('base64');
        const model = process.env.AZURE_OPENAI_MODEL || 'gpt-4o-mini-audio-preview';
        console.log(`🌐 Using model: ${model}`);
        const requestPayload = {
            model: model,
            messages: [
                {
                    role: 'user',
                    content: [
                        {
                            type: 'text',
                            text: 'Please transcribe the audio and improve it with proper grammar, punctuation, and formatting. Make it clear and professional. Respond only with the improved text, no additional commentary.'
                        },
                        {
                            type: 'input_audio',
                            input_audio: {
                                data: audioBase64,
                                format: 'wav'
                            }
                        }
                    ]
                }
            ],
            temperature: 0.8,
            max_tokens: 500
        };
        console.log('📤 Request payload:', JSON.stringify({
            model: requestPayload.model,
            messages: [{
                    role: 'user',
                    content: [
                        { type: 'text', text: requestPayload.messages[0].content[0].text },
                        { type: 'input_audio', input_audio: { format: 'wav', data: '[base64 audio data]' } }
                    ]
                }],
            temperature: requestPayload.temperature,
            max_tokens: requestPayload.max_tokens
        }, null, 2));
        const axios = require('axios');
        const response = await axios.post(`${process.env.AZURE_OPENAI_ENDPOINT}/openai/deployments/${model}/chat/completions?api-version=2024-12-01-preview`, requestPayload, {
            headers: {
                'Authorization': `Bearer ${process.env.AZURE_OPENAI_API_KEY}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        });
        console.log('📊 Response status:', response.status);
        console.log('📊 Response headers:', response.headers);
        if (response.data?.usage) {
            const usage = response.data.usage;
            console.log(`📊 Token usage:`);
            console.log(`  Prompt tokens: ${usage.prompt_tokens}`);
            console.log(`  Completion tokens: ${usage.completion_tokens}`);
            console.log(`  Total tokens: ${usage.total_tokens}`);
            // 計算估計成本
            const promptCost = (usage.prompt_tokens / 1000000) * 0.15;
            const completionCost = (usage.completion_tokens / 1000000) * 0.60;
            const totalCost = promptCost + completionCost;
            console.log(`  Estimated cost: $${totalCost.toFixed(6)}`);
        }
        const result = response.data?.choices?.[0]?.message?.content;
        if (!result) {
            throw new Error('No AI response received');
        }
        console.log('🎯 AI Response:', result.substring(0, 100) + '...');
        return result.trim();
    }
    // 實時轉錄方法 - 立即開始 WebSocket 連接和音頻串流
    async startRealtimeTranscription(audioDevice, audioService, onPartialTranscription, onFinalTranscription, onComplete, onVolumeUpdate) {
        console.log('🔄 Starting real-time transcription with immediate WebSocket connection...');
        return new Promise((resolve, reject) => {
            const model = process.env.AZURE_OPENAI_TRANSCRIBE_MODEL || 'gpt-4o-mini-transcribe';
            console.log(`🎯 Using transcription model: ${model}`);
            // 創建 WebSocket 連接
            const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT?.replace('https://', '').replace('http://', '');
            const wsUrl = `wss://${azureEndpoint}/openai/realtime?api-version=2025-04-01-preview&intent=transcription&deployment=${model}`;
            console.log('🔗 Connecting to Azure OpenAI Realtime WebSocket:', wsUrl);
            // 設置連接超時 (15 秒)
            const connectionTimeout = setTimeout(() => {
                console.error('❌ WebSocket connection timeout after 15 seconds');
                if (this.realtimeWebSocket) {
                    this.realtimeWebSocket.close();
                    this.realtimeWebSocket = null;
                }
                reject(new Error('WebSocket connection timeout'));
            }, 15000);
            // 設置整體會話超時 (5 分鐘)
            const sessionTimeout = setTimeout(() => {
                console.log('⏰ WebSocket session timeout after 5 minutes, closing connection...');
                this.stopRealtimeTranscription();
                onComplete?.();
                resolve();
            }, 300000); // 5 分鐘
            this.realtimeWebSocket = new ws_1.default(wsUrl, {
                headers: {
                    'api-key': process.env.AZURE_OPENAI_API_KEY,
                    'OpenAI-Beta': 'realtime=v1'
                }
            });
            this.realtimeWebSocket.on('open', () => {
                console.log('✅ REALTIME WebSocket connected successfully!');
                console.log('🔗 WebSocket readyState:', this.realtimeWebSocket?.readyState);
                console.log('🎯 Setting up transcription session...');
                // 設置轉錄會話 - 啟用實時轉錄
                const sessionConfig = {
                    type: 'transcription_session.update',
                    session: {
                        input_audio_format: 'pcm16',
                        input_audio_transcription: {
                            model: model,
                            prompt: 'Transcribe speech to text accurately. Users could primarily comes from Hong Kong or China or Foreigners, so high change they speak Cantonese, Mandarin, or other Chinese language, and then English, of course Hong Kong has many foreigners from Europe and Asia as well.'
                        },
                        turn_detection: {
                            type: 'server_vad',
                            threshold: 0.01, // 進一步降低閾值，更敏感的語音檢測
                            prefix_padding_ms: 50, // 進一步減少前綴填充，更快開始
                            silence_duration_ms: 100 // 進一步減少靜音檢測時間，0.3秒後就回應
                        },
                        // 啟用實時轉錄
                        tools: [],
                        tool_choice: 'auto'
                    }
                };
                console.log('📤 Sending session config:', JSON.stringify(sessionConfig, null, 2));
                this.realtimeWebSocket.send(JSON.stringify(sessionConfig));
                console.log('📤 Sent session configuration');
                // 立即發送音頻緩衝區提交，開始轉錄
                setTimeout(() => {
                    if (this.realtimeWebSocket && this.realtimeWebSocket.readyState === ws_1.default.OPEN) {
                        const commitMessage = {
                            type: 'input_audio_buffer.commit'
                        };
                        console.log('📤 Sending audio buffer commit to start transcription');
                        this.realtimeWebSocket.send(JSON.stringify(commitMessage));
                    }
                }, 100);
                // 立即開始實時音頻串流
                this.startRealtimeAudioStreaming(audioService, audioDevice, onVolumeUpdate);
            });
            let partialTranscription = '';
            let finalTranscription = '';
            this.realtimeWebSocket.on('message', (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    // // 詳細的 OpenAI 響應日誌
                    // if (message.type.includes('transcription')) {
                    //   console.log('🤖 OPENAI RESPONSE:', message.type)
                    //   if (message.delta) {
                    //     console.log('📝 REAL-TIME DELTA:', JSON.stringify(message.delta))
                    //   }
                    //   if (message.transcript) {
                    //     console.log('✅ FINAL TRANSCRIPT:', JSON.stringify(message.transcript))
                    //   }
                    // } else {
                    //   console.log('📥 OpenAI message:', message.type)
                    // }
                    switch (message.type) {
                        case 'conversation.item.input_audio_transcription.delta':
                            const delta = message.delta || '';
                            partialTranscription += delta;
                            console.log('🔄 REALTIME DELTA:', delta);
                            console.log('🔄 PARTIAL TRANSCRIPTION:', partialTranscription);
                            onPartialTranscription?.(partialTranscription);
                            // 實時串流文字輸入 - 降低門檻，更快輸入
                            if (partialTranscription.trim().length > 1) {
                                this.textInputService.inputTextStreaming(partialTranscription).catch(console.error);
                            }
                            break;
                        // NOT SEEING THIS HAPPEN
                        // case 'input_audio_buffer.transcription.delta':
                        //   // 另一種可能的實時轉錄格式
                        //   const bufferDelta = message.delta || ''
                        //   partialTranscription += bufferDelta
                        //   console.log('🔄 BUFFER DELTA:', bufferDelta)
                        //   console.log('🔄 BUFFER PARTIAL:', partialTranscription)
                        //   onPartialTranscription?.(partialTranscription)
                        //   if (partialTranscription.trim().length > 1) {
                        //     this.textInputService.inputTextStreaming(partialTranscription).catch(console.error)
                        //   }
                        //   break
                        case 'conversation.item.input_audio_transcription.completed':
                            finalTranscription = message.transcript || '';
                            console.log('✅ Final transcription:', finalTranscription);
                            onFinalTranscription?.(finalTranscription);
                            // 確保最終文字完整輸入
                            this.textInputService.inputTextStreaming(finalTranscription).catch(console.error);
                            // 自動停止：轉錄完成後自動關閉連接和窗口
                            console.log('🔄 Auto-stopping transcription after completion...');
                            setTimeout(() => {
                                this.stopRealtimeTranscription();
                                onComplete?.();
                                resolve();
                            }, 1000);
                            break;
                        case 'transcription_session.created':
                            console.log('✅ Transcription session created');
                            break;
                        case 'transcription_session.updated':
                            console.log('✅ Transcription session updated');
                            break;
                        case 'input_audio_buffer.speech_started':
                            console.log('🎤 Speech started detected');
                            break;
                        case 'input_audio_buffer.speech_stopped':
                            console.log('🛑 Speech stopped detected');
                            break;
                        case 'input_audio_buffer.committed':
                            console.log('📥 Other message type:', message.type);
                            break;
                        case 'conversation.item.created':
                            console.log('📥 Other message type:', message.type);
                            break;
                        default:
                            console.log('📥 Other message type:', message.type);
                            break;
                    }
                }
                catch (error) {
                    console.error('❌ Failed to parse WebSocket message:', error);
                }
            });
            this.realtimeWebSocket.on('close', () => {
                console.log('🔌 WebSocket connection closed');
                resolve();
            });
            this.realtimeWebSocket.on('error', (error) => {
                console.error('❌ WebSocket error:', error);
                reject(error);
            });
        });
    }
    startRealtimeAudioStreaming(audioService, audioDevice, onVolumeUpdate) {
        console.log('🎤 Starting realtime audio streaming...');
        try {
            // 創建錄音進程
            this.realtimeRecordingProcess = audioService.createRecordingProcess(audioDevice);
            // 實時處理音頻數據並發送到 WebSocket
            let audioChunkCount = 0;
            this.realtimeRecordingProcess.stdout?.on('data', (chunk) => {
                if (this.realtimeWebSocket && this.realtimeWebSocket.readyState === ws_1.default.OPEN) {
                    // 跳過 WAV 標頭，獲取 PCM 數據
                    const pcmData = chunk.length > 44 ? chunk.subarray(44) : chunk;
                    if (pcmData.length > 0) {
                        audioChunkCount++;
                        const audioBase64 = pcmData.toString('base64');
                        const audioMessage = {
                            type: 'input_audio_buffer.append',
                            audio: audioBase64
                        };
                        // 發送音頻到 OpenAI
                        try {
                            this.realtimeWebSocket.send(JSON.stringify(audioMessage));
                            // 詳細日誌：每個音頻塊都記錄
                            // console.log(`📤 AUDIO SENT: Chunk #${audioChunkCount}, ${pcmData.length} bytes, base64 length: ${audioBase64.length}`)
                        }
                        catch (error) {
                            console.error(`❌ Failed to send audio chunk #${audioChunkCount}:`, error);
                        }
                        // 計算音量級別 (0-100)
                        const samples = new Int16Array(pcmData.buffer, pcmData.byteOffset, pcmData.length / 2);
                        const maxAmplitude = Math.max(...Array.from(samples).map(Math.abs));
                        const volume = Math.min(100, Math.round((maxAmplitude / 32767) * 100));
                        // 發送音量更新
                        if (onVolumeUpdate) {
                            onVolumeUpdate(volume);
                        }
                        // 每10個音頻塊顯示一次詳細狀態
                        // if (audioChunkCount % 10 === 0) {
                        //   console.log(`🎤 STREAMING STATUS: Chunk #${audioChunkCount}, PCM: ${pcmData.length} bytes, Volume: ${volume}%, WebSocket: ${this.realtimeWebSocket.readyState === WebSocket.OPEN ? 'OPEN' : 'CLOSED'}`)
                        // }
                    }
                    else {
                        console.log(`⚠️ Empty PCM data received, chunk length: ${chunk.length}`);
                    }
                }
            });
            this.realtimeRecordingProcess.stderr?.on('data', (data) => {
                const errorMsg = data.toString();
                // 只顯示重要的錯誤信息
                if (!errorMsg.includes('size=') && !errorMsg.includes('time=') && !errorMsg.includes('bitrate=')) {
                    console.log('🔧 FFmpeg stderr:', errorMsg.trim());
                }
            });
            this.realtimeRecordingProcess.on('close', (code) => {
                console.log(`🎙️ Realtime recording finished with code: ${code}`);
            });
            console.log('✅ Realtime audio streaming started');
        }
        catch (error) {
            console.error('❌ Failed to start realtime audio streaming:', error);
            throw error;
        }
    }
    stopRealtimeTranscription() {
        console.log('🛑 Stopping realtime transcription...');
        if (this.realtimeRecordingProcess) {
            this.realtimeRecordingProcess.kill('SIGINT');
            this.realtimeRecordingProcess = null;
            console.log('📝 Stopped realtime recording process');
        }
        if (this.realtimeWebSocket && this.realtimeWebSocket.readyState === ws_1.default.OPEN) {
            this.realtimeWebSocket.close();
            this.realtimeWebSocket = null;
            console.log('🔌 Closed realtime WebSocket');
        }
    }
}
exports.AIService = AIService;
