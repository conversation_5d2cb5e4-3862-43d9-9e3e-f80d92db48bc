var Pm=Object.defineProperty;var Em=(e,t,n)=>t in e?Pm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var tt=(e,t,n)=>Em(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();const Tm="modulepreload",Rm=function(e){return"/"+e},du={},Am=function(t,n,r){let i=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),a=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));i=Promise.allSettled(n.map(l=>{if(l=Rm(l),l in du)return;du[l]=!0;const u=l.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${d}`))return;const c=document.createElement("link");if(c.rel=u?"stylesheet":Tm,u||(c.as="script"),c.crossOrigin="",c.href=l,a&&c.setAttribute("nonce",a),document.head.appendChild(c),u)return new Promise((p,g)=>{c.addEventListener("load",p),c.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${l}`)))})}))}function o(s){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=s,window.dispatchEvent(a),!a.defaultPrevented)throw s}return i.then(s=>{for(const a of s||[])a.status==="rejected"&&o(a.reason);return t().catch(o)})};function jm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ld={exports:{}},Ao={},Nd={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ei=Symbol.for("react.element"),Lm=Symbol.for("react.portal"),Nm=Symbol.for("react.fragment"),Mm=Symbol.for("react.strict_mode"),Dm=Symbol.for("react.profiler"),_m=Symbol.for("react.provider"),Vm=Symbol.for("react.context"),Im=Symbol.for("react.forward_ref"),Om=Symbol.for("react.suspense"),zm=Symbol.for("react.memo"),Fm=Symbol.for("react.lazy"),fu=Symbol.iterator;function Bm(e){return e===null||typeof e!="object"?null:(e=fu&&e[fu]||e["@@iterator"],typeof e=="function"?e:null)}var Md={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Dd=Object.assign,_d={};function nr(e,t,n){this.props=e,this.context=t,this.refs=_d,this.updater=n||Md}nr.prototype.isReactComponent={};nr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};nr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Vd(){}Vd.prototype=nr.prototype;function Qa(e,t,n){this.props=e,this.context=t,this.refs=_d,this.updater=n||Md}var Ya=Qa.prototype=new Vd;Ya.constructor=Qa;Dd(Ya,nr.prototype);Ya.isPureReactComponent=!0;var pu=Array.isArray,Id=Object.prototype.hasOwnProperty,Xa={current:null},Od={key:!0,ref:!0,__self:!0,__source:!0};function zd(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Id.call(t,r)&&!Od.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:ei,type:e,key:o,ref:s,props:i,_owner:Xa.current}}function Um(e,t){return{$$typeof:ei,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Za(e){return typeof e=="object"&&e!==null&&e.$$typeof===ei}function bm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var hu=/\/+/g;function ts(e,t){return typeof e=="object"&&e!==null&&e.key!=null?bm(""+e.key):t.toString(36)}function Mi(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ei:case Lm:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+ts(s,0):r,pu(i)?(n="",e!=null&&(n=e.replace(hu,"$&/")+"/"),Mi(i,t,n,"",function(u){return u})):i!=null&&(Za(i)&&(i=Um(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(hu,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",pu(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+ts(o,a);s+=Mi(o,t,n,l,i)}else if(l=Bm(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+ts(o,a++),s+=Mi(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function di(e,t,n){if(e==null)return e;var r=[],i=0;return Mi(e,r,"","",function(o){return t.call(n,o,i++)}),r}function $m(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ke={current:null},Di={transition:null},Wm={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:Di,ReactCurrentOwner:Xa};function Fd(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:di,forEach:function(e,t,n){di(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return di(e,function(){t++}),t},toArray:function(e){return di(e,function(t){return t})||[]},only:function(e){if(!Za(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};I.Component=nr;I.Fragment=Nm;I.Profiler=Dm;I.PureComponent=Qa;I.StrictMode=Mm;I.Suspense=Om;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Wm;I.act=Fd;I.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Dd({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Xa.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Id.call(t,l)&&!Od.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ei,type:e.type,key:i,ref:o,props:r,_owner:s}};I.createContext=function(e){return e={$$typeof:Vm,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:_m,_context:e},e.Consumer=e};I.createElement=zd;I.createFactory=function(e){var t=zd.bind(null,e);return t.type=e,t};I.createRef=function(){return{current:null}};I.forwardRef=function(e){return{$$typeof:Im,render:e}};I.isValidElement=Za;I.lazy=function(e){return{$$typeof:Fm,_payload:{_status:-1,_result:e},_init:$m}};I.memo=function(e,t){return{$$typeof:zm,type:e,compare:t===void 0?null:t}};I.startTransition=function(e){var t=Di.transition;Di.transition={};try{e()}finally{Di.transition=t}};I.unstable_act=Fd;I.useCallback=function(e,t){return ke.current.useCallback(e,t)};I.useContext=function(e){return ke.current.useContext(e)};I.useDebugValue=function(){};I.useDeferredValue=function(e){return ke.current.useDeferredValue(e)};I.useEffect=function(e,t){return ke.current.useEffect(e,t)};I.useId=function(){return ke.current.useId()};I.useImperativeHandle=function(e,t,n){return ke.current.useImperativeHandle(e,t,n)};I.useInsertionEffect=function(e,t){return ke.current.useInsertionEffect(e,t)};I.useLayoutEffect=function(e,t){return ke.current.useLayoutEffect(e,t)};I.useMemo=function(e,t){return ke.current.useMemo(e,t)};I.useReducer=function(e,t,n){return ke.current.useReducer(e,t,n)};I.useRef=function(e){return ke.current.useRef(e)};I.useState=function(e){return ke.current.useState(e)};I.useSyncExternalStore=function(e,t,n){return ke.current.useSyncExternalStore(e,t,n)};I.useTransition=function(){return ke.current.useTransition()};I.version="18.3.1";Nd.exports=I;var E=Nd.exports;const jo=jm(E);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hm=E,Km=Symbol.for("react.element"),Gm=Symbol.for("react.fragment"),Qm=Object.prototype.hasOwnProperty,Ym=Hm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Xm={key:!0,ref:!0,__self:!0,__source:!0};function Bd(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Qm.call(t,r)&&!Xm.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Km,type:e,key:o,ref:s,props:i,_owner:Ym.current}}Ao.Fragment=Gm;Ao.jsx=Bd;Ao.jsxs=Bd;Ld.exports=Ao;var y=Ld.exports,Bs={},Ud={exports:{}},Ie={},bd={exports:{}},$d={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(A,D){var V=A.length;A.push(D);e:for(;0<V;){var _=V-1>>>1,W=A[_];if(0<i(W,D))A[_]=D,A[V]=W,V=_;else break e}}function n(A){return A.length===0?null:A[0]}function r(A){if(A.length===0)return null;var D=A[0],V=A.pop();if(V!==D){A[0]=V;e:for(var _=0,W=A.length,Xt=W>>>1;_<Xt;){var et=2*(_+1)-1,kn=A[et],Le=et+1,Zt=A[Le];if(0>i(kn,V))Le<W&&0>i(Zt,kn)?(A[_]=Zt,A[Le]=V,_=Le):(A[_]=kn,A[et]=V,_=et);else if(Le<W&&0>i(Zt,V))A[_]=Zt,A[Le]=V,_=Le;else break e}}return D}function i(A,D){var V=A.sortIndex-D.sortIndex;return V!==0?V:A.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],d=1,c=null,p=3,g=!1,v=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,f=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(A){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=A)r(u),D.sortIndex=D.expirationTime,t(l,D);else break;D=n(u)}}function w(A){if(x=!1,h(A),!v)if(n(l)!==null)v=!0,Z(k);else{var D=n(u);D!==null&&ze(w,D.startTime-A)}}function k(A,D){v=!1,x&&(x=!1,m(R),R=-1),g=!0;var V=p;try{for(h(D),c=n(l);c!==null&&(!(c.expirationTime>D)||A&&!oe());){var _=c.callback;if(typeof _=="function"){c.callback=null,p=c.priorityLevel;var W=_(c.expirationTime<=D);D=e.unstable_now(),typeof W=="function"?c.callback=W:c===n(l)&&r(l),h(D)}else r(l);c=n(l)}if(c!==null)var Xt=!0;else{var et=n(u);et!==null&&ze(w,et.startTime-D),Xt=!1}return Xt}finally{c=null,p=V,g=!1}}var T=!1,C=null,R=-1,L=5,M=-1;function oe(){return!(e.unstable_now()-M<L)}function ue(){if(C!==null){var A=e.unstable_now();M=A;var D=!0;try{D=C(!0,A)}finally{D?ve():(T=!1,C=null)}}else T=!1}var ve;if(typeof f=="function")ve=function(){f(ue)};else if(typeof MessageChannel<"u"){var se=new MessageChannel,Ct=se.port2;se.port1.onmessage=ue,ve=function(){Ct.postMessage(null)}}else ve=function(){S(ue,0)};function Z(A){C=A,T||(T=!0,ve())}function ze(A,D){R=S(function(){A(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(A){A.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,Z(k))},e.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<A?Math.floor(1e3/A):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(A){switch(p){case 1:case 2:case 3:var D=3;break;default:D=p}var V=p;p=D;try{return A()}finally{p=V}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(A,D){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var V=p;p=A;try{return D()}finally{p=V}},e.unstable_scheduleCallback=function(A,D,V){var _=e.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?_+V:_):V=_,A){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=V+W,A={id:d++,callback:D,priorityLevel:A,startTime:V,expirationTime:W,sortIndex:-1},V>_?(A.sortIndex=V,t(u,A),n(l)===null&&A===n(u)&&(x?(m(R),R=-1):x=!0,ze(w,V-_))):(A.sortIndex=W,t(l,A),v||g||(v=!0,Z(k))),A},e.unstable_shouldYield=oe,e.unstable_wrapCallback=function(A){var D=p;return function(){var V=p;p=D;try{return A.apply(this,arguments)}finally{p=V}}}})($d);bd.exports=$d;var Zm=bd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qm=E,_e=Zm;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Wd=new Set,_r={};function vn(e,t){Gn(e,t),Gn(e+"Capture",t)}function Gn(e,t){for(_r[e]=t,e=0;e<t.length;e++)Wd.add(t[e])}var yt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Us=Object.prototype.hasOwnProperty,Jm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,mu={},gu={};function eg(e){return Us.call(gu,e)?!0:Us.call(mu,e)?!1:Jm.test(e)?gu[e]=!0:(mu[e]=!0,!1)}function tg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ng(e,t,n,r){if(t===null||typeof t>"u"||tg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ce(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new Ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new Ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new Ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new Ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new Ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new Ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new Ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new Ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new Ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var qa=/[\-:]([a-z])/g;function Ja(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(qa,Ja);fe[t]=new Ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(qa,Ja);fe[t]=new Ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(qa,Ja);fe[t]=new Ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new Ce(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new Ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new Ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function el(e,t,n,r){var i=fe.hasOwnProperty(t)?fe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ng(t,n,i,r)&&(n=null),r||i===null?eg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var kt=qm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,fi=Symbol.for("react.element"),Pn=Symbol.for("react.portal"),En=Symbol.for("react.fragment"),tl=Symbol.for("react.strict_mode"),bs=Symbol.for("react.profiler"),Hd=Symbol.for("react.provider"),Kd=Symbol.for("react.context"),nl=Symbol.for("react.forward_ref"),$s=Symbol.for("react.suspense"),Ws=Symbol.for("react.suspense_list"),rl=Symbol.for("react.memo"),Tt=Symbol.for("react.lazy"),Gd=Symbol.for("react.offscreen"),yu=Symbol.iterator;function or(e){return e===null||typeof e!="object"?null:(e=yu&&e[yu]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,ns;function gr(e){if(ns===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ns=t&&t[1]||""}return`
`+ns+e}var rs=!1;function is(e,t){if(!e||rs)return"";rs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var l=`
`+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{rs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?gr(e):""}function rg(e){switch(e.tag){case 5:return gr(e.type);case 16:return gr("Lazy");case 13:return gr("Suspense");case 19:return gr("SuspenseList");case 0:case 2:case 15:return e=is(e.type,!1),e;case 11:return e=is(e.type.render,!1),e;case 1:return e=is(e.type,!0),e;default:return""}}function Hs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case En:return"Fragment";case Pn:return"Portal";case bs:return"Profiler";case tl:return"StrictMode";case $s:return"Suspense";case Ws:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Kd:return(e.displayName||"Context")+".Consumer";case Hd:return(e._context.displayName||"Context")+".Provider";case nl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case rl:return t=e.displayName||null,t!==null?t:Hs(e.type)||"Memo";case Tt:t=e._payload,e=e._init;try{return Hs(e(t))}catch{}}return null}function ig(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Hs(t);case 8:return t===tl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function bt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Qd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function og(e){var t=Qd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function pi(e){e._valueTracker||(e._valueTracker=og(e))}function Yd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Qd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Gi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ks(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function vu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=bt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Xd(e,t){t=t.checked,t!=null&&el(e,"checked",t,!1)}function Gs(e,t){Xd(e,t);var n=bt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Qs(e,t.type,n):t.hasOwnProperty("defaultValue")&&Qs(e,t.type,bt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function xu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Qs(e,t,n){(t!=="number"||Gi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var yr=Array.isArray;function Bn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+bt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ys(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function wu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(yr(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:bt(n)}}function Zd(e,t){var n=bt(t.value),r=bt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Su(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function qd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Xs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?qd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var hi,Jd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(hi=hi||document.createElement("div"),hi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=hi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Sr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},sg=["Webkit","ms","Moz","O"];Object.keys(Sr).forEach(function(e){sg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Sr[t]=Sr[e]})});function ef(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Sr.hasOwnProperty(e)&&Sr[e]?(""+t).trim():t+"px"}function tf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=ef(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var ag=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Zs(e,t){if(t){if(ag[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function qs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Js=null;function il(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ea=null,Un=null,bn=null;function ku(e){if(e=ri(e)){if(typeof ea!="function")throw Error(P(280));var t=e.stateNode;t&&(t=_o(t),ea(e.stateNode,e.type,t))}}function nf(e){Un?bn?bn.push(e):bn=[e]:Un=e}function rf(){if(Un){var e=Un,t=bn;if(bn=Un=null,ku(e),t)for(e=0;e<t.length;e++)ku(t[e])}}function of(e,t){return e(t)}function sf(){}var os=!1;function af(e,t,n){if(os)return e(t,n);os=!0;try{return of(e,t,n)}finally{os=!1,(Un!==null||bn!==null)&&(sf(),rf())}}function Ir(e,t){var n=e.stateNode;if(n===null)return null;var r=_o(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var ta=!1;if(yt)try{var sr={};Object.defineProperty(sr,"passive",{get:function(){ta=!0}}),window.addEventListener("test",sr,sr),window.removeEventListener("test",sr,sr)}catch{ta=!1}function lg(e,t,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var kr=!1,Qi=null,Yi=!1,na=null,ug={onError:function(e){kr=!0,Qi=e}};function cg(e,t,n,r,i,o,s,a,l){kr=!1,Qi=null,lg.apply(ug,arguments)}function dg(e,t,n,r,i,o,s,a,l){if(cg.apply(this,arguments),kr){if(kr){var u=Qi;kr=!1,Qi=null}else throw Error(P(198));Yi||(Yi=!0,na=u)}}function xn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function lf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Cu(e){if(xn(e)!==e)throw Error(P(188))}function fg(e){var t=e.alternate;if(!t){if(t=xn(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Cu(i),e;if(o===r)return Cu(i),t;o=o.sibling}throw Error(P(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function uf(e){return e=fg(e),e!==null?cf(e):null}function cf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=cf(e);if(t!==null)return t;e=e.sibling}return null}var df=_e.unstable_scheduleCallback,Pu=_e.unstable_cancelCallback,pg=_e.unstable_shouldYield,hg=_e.unstable_requestPaint,q=_e.unstable_now,mg=_e.unstable_getCurrentPriorityLevel,ol=_e.unstable_ImmediatePriority,ff=_e.unstable_UserBlockingPriority,Xi=_e.unstable_NormalPriority,gg=_e.unstable_LowPriority,pf=_e.unstable_IdlePriority,Lo=null,st=null;function yg(e){if(st&&typeof st.onCommitFiberRoot=="function")try{st.onCommitFiberRoot(Lo,e,void 0,(e.current.flags&128)===128)}catch{}}var Ze=Math.clz32?Math.clz32:wg,vg=Math.log,xg=Math.LN2;function wg(e){return e>>>=0,e===0?32:31-(vg(e)/xg|0)|0}var mi=64,gi=4194304;function vr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Zi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=vr(a):(o&=s,o!==0&&(r=vr(o)))}else s=n&~i,s!==0?r=vr(s):o!==0&&(r=vr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ze(t),i=1<<n,r|=e[n],t&=~i;return r}function Sg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function kg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ze(o),a=1<<s,l=i[s];l===-1?(!(a&n)||a&r)&&(i[s]=Sg(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function ra(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function hf(){var e=mi;return mi<<=1,!(mi&4194240)&&(mi=64),e}function ss(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ti(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ze(t),e[t]=n}function Cg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ze(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function sl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ze(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var z=0;function mf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var gf,al,yf,vf,xf,ia=!1,yi=[],Dt=null,_t=null,Vt=null,Or=new Map,zr=new Map,jt=[],Pg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Eu(e,t){switch(e){case"focusin":case"focusout":Dt=null;break;case"dragenter":case"dragleave":_t=null;break;case"mouseover":case"mouseout":Vt=null;break;case"pointerover":case"pointerout":Or.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":zr.delete(t.pointerId)}}function ar(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=ri(t),t!==null&&al(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Eg(e,t,n,r,i){switch(t){case"focusin":return Dt=ar(Dt,e,t,n,r,i),!0;case"dragenter":return _t=ar(_t,e,t,n,r,i),!0;case"mouseover":return Vt=ar(Vt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Or.set(o,ar(Or.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,zr.set(o,ar(zr.get(o)||null,e,t,n,r,i)),!0}return!1}function wf(e){var t=sn(e.target);if(t!==null){var n=xn(t);if(n!==null){if(t=n.tag,t===13){if(t=lf(n),t!==null){e.blockedOn=t,xf(e.priority,function(){yf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function _i(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=oa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Js=r,n.target.dispatchEvent(r),Js=null}else return t=ri(n),t!==null&&al(t),e.blockedOn=n,!1;t.shift()}return!0}function Tu(e,t,n){_i(e)&&n.delete(t)}function Tg(){ia=!1,Dt!==null&&_i(Dt)&&(Dt=null),_t!==null&&_i(_t)&&(_t=null),Vt!==null&&_i(Vt)&&(Vt=null),Or.forEach(Tu),zr.forEach(Tu)}function lr(e,t){e.blockedOn===t&&(e.blockedOn=null,ia||(ia=!0,_e.unstable_scheduleCallback(_e.unstable_NormalPriority,Tg)))}function Fr(e){function t(i){return lr(i,e)}if(0<yi.length){lr(yi[0],e);for(var n=1;n<yi.length;n++){var r=yi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Dt!==null&&lr(Dt,e),_t!==null&&lr(_t,e),Vt!==null&&lr(Vt,e),Or.forEach(t),zr.forEach(t),n=0;n<jt.length;n++)r=jt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<jt.length&&(n=jt[0],n.blockedOn===null);)wf(n),n.blockedOn===null&&jt.shift()}var $n=kt.ReactCurrentBatchConfig,qi=!0;function Rg(e,t,n,r){var i=z,o=$n.transition;$n.transition=null;try{z=1,ll(e,t,n,r)}finally{z=i,$n.transition=o}}function Ag(e,t,n,r){var i=z,o=$n.transition;$n.transition=null;try{z=4,ll(e,t,n,r)}finally{z=i,$n.transition=o}}function ll(e,t,n,r){if(qi){var i=oa(e,t,n,r);if(i===null)gs(e,t,r,Ji,n),Eu(e,r);else if(Eg(i,e,t,n,r))r.stopPropagation();else if(Eu(e,r),t&4&&-1<Pg.indexOf(e)){for(;i!==null;){var o=ri(i);if(o!==null&&gf(o),o=oa(e,t,n,r),o===null&&gs(e,t,r,Ji,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else gs(e,t,r,null,n)}}var Ji=null;function oa(e,t,n,r){if(Ji=null,e=il(r),e=sn(e),e!==null)if(t=xn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=lf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ji=e,null}function Sf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(mg()){case ol:return 1;case ff:return 4;case Xi:case gg:return 16;case pf:return 536870912;default:return 16}default:return 16}}var Nt=null,ul=null,Vi=null;function kf(){if(Vi)return Vi;var e,t=ul,n=t.length,r,i="value"in Nt?Nt.value:Nt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Vi=i.slice(e,1<r?1-r:void 0)}function Ii(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function vi(){return!0}function Ru(){return!1}function Oe(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?vi:Ru,this.isPropagationStopped=Ru,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=vi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=vi)},persist:function(){},isPersistent:vi}),t}var rr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cl=Oe(rr),ni=Y({},rr,{view:0,detail:0}),jg=Oe(ni),as,ls,ur,No=Y({},ni,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:dl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ur&&(ur&&e.type==="mousemove"?(as=e.screenX-ur.screenX,ls=e.screenY-ur.screenY):ls=as=0,ur=e),as)},movementY:function(e){return"movementY"in e?e.movementY:ls}}),Au=Oe(No),Lg=Y({},No,{dataTransfer:0}),Ng=Oe(Lg),Mg=Y({},ni,{relatedTarget:0}),us=Oe(Mg),Dg=Y({},rr,{animationName:0,elapsedTime:0,pseudoElement:0}),_g=Oe(Dg),Vg=Y({},rr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ig=Oe(Vg),Og=Y({},rr,{data:0}),ju=Oe(Og),zg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Bg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ug(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Bg[e])?!!t[e]:!1}function dl(){return Ug}var bg=Y({},ni,{key:function(e){if(e.key){var t=zg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ii(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:dl,charCode:function(e){return e.type==="keypress"?Ii(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ii(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),$g=Oe(bg),Wg=Y({},No,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Lu=Oe(Wg),Hg=Y({},ni,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:dl}),Kg=Oe(Hg),Gg=Y({},rr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qg=Oe(Gg),Yg=Y({},No,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Xg=Oe(Yg),Zg=[9,13,27,32],fl=yt&&"CompositionEvent"in window,Cr=null;yt&&"documentMode"in document&&(Cr=document.documentMode);var qg=yt&&"TextEvent"in window&&!Cr,Cf=yt&&(!fl||Cr&&8<Cr&&11>=Cr),Nu=" ",Mu=!1;function Pf(e,t){switch(e){case"keyup":return Zg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ef(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Tn=!1;function Jg(e,t){switch(e){case"compositionend":return Ef(t);case"keypress":return t.which!==32?null:(Mu=!0,Nu);case"textInput":return e=t.data,e===Nu&&Mu?null:e;default:return null}}function ey(e,t){if(Tn)return e==="compositionend"||!fl&&Pf(e,t)?(e=kf(),Vi=ul=Nt=null,Tn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cf&&t.locale!=="ko"?null:t.data;default:return null}}var ty={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Du(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!ty[e.type]:t==="textarea"}function Tf(e,t,n,r){nf(r),t=eo(t,"onChange"),0<t.length&&(n=new cl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Pr=null,Br=null;function ny(e){Of(e,0)}function Mo(e){var t=jn(e);if(Yd(t))return e}function ry(e,t){if(e==="change")return t}var Rf=!1;if(yt){var cs;if(yt){var ds="oninput"in document;if(!ds){var _u=document.createElement("div");_u.setAttribute("oninput","return;"),ds=typeof _u.oninput=="function"}cs=ds}else cs=!1;Rf=cs&&(!document.documentMode||9<document.documentMode)}function Vu(){Pr&&(Pr.detachEvent("onpropertychange",Af),Br=Pr=null)}function Af(e){if(e.propertyName==="value"&&Mo(Br)){var t=[];Tf(t,Br,e,il(e)),af(ny,t)}}function iy(e,t,n){e==="focusin"?(Vu(),Pr=t,Br=n,Pr.attachEvent("onpropertychange",Af)):e==="focusout"&&Vu()}function oy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Mo(Br)}function sy(e,t){if(e==="click")return Mo(t)}function ay(e,t){if(e==="input"||e==="change")return Mo(t)}function ly(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Je=typeof Object.is=="function"?Object.is:ly;function Ur(e,t){if(Je(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Us.call(t,i)||!Je(e[i],t[i]))return!1}return!0}function Iu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ou(e,t){var n=Iu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Iu(n)}}function jf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?jf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Lf(){for(var e=window,t=Gi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Gi(e.document)}return t}function pl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function uy(e){var t=Lf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jf(n.ownerDocument.documentElement,n)){if(r!==null&&pl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Ou(n,o);var s=Ou(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var cy=yt&&"documentMode"in document&&11>=document.documentMode,Rn=null,sa=null,Er=null,aa=!1;function zu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;aa||Rn==null||Rn!==Gi(r)||(r=Rn,"selectionStart"in r&&pl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Er&&Ur(Er,r)||(Er=r,r=eo(sa,"onSelect"),0<r.length&&(t=new cl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Rn)))}function xi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var An={animationend:xi("Animation","AnimationEnd"),animationiteration:xi("Animation","AnimationIteration"),animationstart:xi("Animation","AnimationStart"),transitionend:xi("Transition","TransitionEnd")},fs={},Nf={};yt&&(Nf=document.createElement("div").style,"AnimationEvent"in window||(delete An.animationend.animation,delete An.animationiteration.animation,delete An.animationstart.animation),"TransitionEvent"in window||delete An.transitionend.transition);function Do(e){if(fs[e])return fs[e];if(!An[e])return e;var t=An[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Nf)return fs[e]=t[n];return e}var Mf=Do("animationend"),Df=Do("animationiteration"),_f=Do("animationstart"),Vf=Do("transitionend"),If=new Map,Fu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Kt(e,t){If.set(e,t),vn(t,[e])}for(var ps=0;ps<Fu.length;ps++){var hs=Fu[ps],dy=hs.toLowerCase(),fy=hs[0].toUpperCase()+hs.slice(1);Kt(dy,"on"+fy)}Kt(Mf,"onAnimationEnd");Kt(Df,"onAnimationIteration");Kt(_f,"onAnimationStart");Kt("dblclick","onDoubleClick");Kt("focusin","onFocus");Kt("focusout","onBlur");Kt(Vf,"onTransitionEnd");Gn("onMouseEnter",["mouseout","mouseover"]);Gn("onMouseLeave",["mouseout","mouseover"]);Gn("onPointerEnter",["pointerout","pointerover"]);Gn("onPointerLeave",["pointerout","pointerover"]);vn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));vn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));vn("onBeforeInput",["compositionend","keypress","textInput","paste"]);vn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));vn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));vn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),py=new Set("cancel close invalid load scroll toggle".split(" ").concat(xr));function Bu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,dg(r,t,void 0,e),e.currentTarget=null}function Of(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break e;Bu(i,a,u),o=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break e;Bu(i,a,u),o=l}}}if(Yi)throw e=na,Yi=!1,na=null,e}function b(e,t){var n=t[fa];n===void 0&&(n=t[fa]=new Set);var r=e+"__bubble";n.has(r)||(zf(t,e,2,!1),n.add(r))}function ms(e,t,n){var r=0;t&&(r|=4),zf(n,e,r,t)}var wi="_reactListening"+Math.random().toString(36).slice(2);function br(e){if(!e[wi]){e[wi]=!0,Wd.forEach(function(n){n!=="selectionchange"&&(py.has(n)||ms(n,!1,e),ms(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[wi]||(t[wi]=!0,ms("selectionchange",!1,t))}}function zf(e,t,n,r){switch(Sf(t)){case 1:var i=Rg;break;case 4:i=Ag;break;default:i=ll}n=i.bind(null,t,n,e),i=void 0,!ta||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function gs(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;s=s.return}for(;a!==null;){if(s=sn(a),s===null)return;if(l=s.tag,l===5||l===6){r=o=s;continue e}a=a.parentNode}}r=r.return}af(function(){var u=o,d=il(n),c=[];e:{var p=If.get(e);if(p!==void 0){var g=cl,v=e;switch(e){case"keypress":if(Ii(n)===0)break e;case"keydown":case"keyup":g=$g;break;case"focusin":v="focus",g=us;break;case"focusout":v="blur",g=us;break;case"beforeblur":case"afterblur":g=us;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Au;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Ng;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Kg;break;case Mf:case Df:case _f:g=_g;break;case Vf:g=Qg;break;case"scroll":g=jg;break;case"wheel":g=Xg;break;case"copy":case"cut":case"paste":g=Ig;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Lu}var x=(t&4)!==0,S=!x&&e==="scroll",m=x?p!==null?p+"Capture":null:p;x=[];for(var f=u,h;f!==null;){h=f;var w=h.stateNode;if(h.tag===5&&w!==null&&(h=w,m!==null&&(w=Ir(f,m),w!=null&&x.push($r(f,w,h)))),S)break;f=f.return}0<x.length&&(p=new g(p,v,null,n,d),c.push({event:p,listeners:x}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",p&&n!==Js&&(v=n.relatedTarget||n.fromElement)&&(sn(v)||v[vt]))break e;if((g||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?sn(v):null,v!==null&&(S=xn(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(x=Au,w="onMouseLeave",m="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(x=Lu,w="onPointerLeave",m="onPointerEnter",f="pointer"),S=g==null?p:jn(g),h=v==null?p:jn(v),p=new x(w,f+"leave",g,n,d),p.target=S,p.relatedTarget=h,w=null,sn(d)===u&&(x=new x(m,f+"enter",v,n,d),x.target=h,x.relatedTarget=S,w=x),S=w,g&&v)t:{for(x=g,m=v,f=0,h=x;h;h=Cn(h))f++;for(h=0,w=m;w;w=Cn(w))h++;for(;0<f-h;)x=Cn(x),f--;for(;0<h-f;)m=Cn(m),h--;for(;f--;){if(x===m||m!==null&&x===m.alternate)break t;x=Cn(x),m=Cn(m)}x=null}else x=null;g!==null&&Uu(c,p,g,x,!1),v!==null&&S!==null&&Uu(c,S,v,x,!0)}}e:{if(p=u?jn(u):window,g=p.nodeName&&p.nodeName.toLowerCase(),g==="select"||g==="input"&&p.type==="file")var k=ry;else if(Du(p))if(Rf)k=ay;else{k=oy;var T=iy}else(g=p.nodeName)&&g.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(k=sy);if(k&&(k=k(e,u))){Tf(c,k,n,d);break e}T&&T(e,p,u),e==="focusout"&&(T=p._wrapperState)&&T.controlled&&p.type==="number"&&Qs(p,"number",p.value)}switch(T=u?jn(u):window,e){case"focusin":(Du(T)||T.contentEditable==="true")&&(Rn=T,sa=u,Er=null);break;case"focusout":Er=sa=Rn=null;break;case"mousedown":aa=!0;break;case"contextmenu":case"mouseup":case"dragend":aa=!1,zu(c,n,d);break;case"selectionchange":if(cy)break;case"keydown":case"keyup":zu(c,n,d)}var C;if(fl)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else Tn?Pf(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(Cf&&n.locale!=="ko"&&(Tn||R!=="onCompositionStart"?R==="onCompositionEnd"&&Tn&&(C=kf()):(Nt=d,ul="value"in Nt?Nt.value:Nt.textContent,Tn=!0)),T=eo(u,R),0<T.length&&(R=new ju(R,e,null,n,d),c.push({event:R,listeners:T}),C?R.data=C:(C=Ef(n),C!==null&&(R.data=C)))),(C=qg?Jg(e,n):ey(e,n))&&(u=eo(u,"onBeforeInput"),0<u.length&&(d=new ju("onBeforeInput","beforeinput",null,n,d),c.push({event:d,listeners:u}),d.data=C))}Of(c,t)})}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function eo(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Ir(e,n),o!=null&&r.unshift($r(e,o,i)),o=Ir(e,t),o!=null&&r.push($r(e,o,i))),e=e.return}return r}function Cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Uu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Ir(n,o),l!=null&&s.unshift($r(n,l,a))):i||(l=Ir(n,o),l!=null&&s.push($r(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var hy=/\r\n?/g,my=/\u0000|\uFFFD/g;function bu(e){return(typeof e=="string"?e:""+e).replace(hy,`
`).replace(my,"")}function Si(e,t,n){if(t=bu(t),bu(e)!==t&&n)throw Error(P(425))}function to(){}var la=null,ua=null;function ca(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var da=typeof setTimeout=="function"?setTimeout:void 0,gy=typeof clearTimeout=="function"?clearTimeout:void 0,$u=typeof Promise=="function"?Promise:void 0,yy=typeof queueMicrotask=="function"?queueMicrotask:typeof $u<"u"?function(e){return $u.resolve(null).then(e).catch(vy)}:da;function vy(e){setTimeout(function(){throw e})}function ys(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Fr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Fr(t)}function It(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Wu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ir=Math.random().toString(36).slice(2),it="__reactFiber$"+ir,Wr="__reactProps$"+ir,vt="__reactContainer$"+ir,fa="__reactEvents$"+ir,xy="__reactListeners$"+ir,wy="__reactHandles$"+ir;function sn(e){var t=e[it];if(t)return t;for(var n=e.parentNode;n;){if(t=n[vt]||n[it]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Wu(e);e!==null;){if(n=e[it])return n;e=Wu(e)}return t}e=n,n=e.parentNode}return null}function ri(e){return e=e[it]||e[vt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function jn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function _o(e){return e[Wr]||null}var pa=[],Ln=-1;function Gt(e){return{current:e}}function $(e){0>Ln||(e.current=pa[Ln],pa[Ln]=null,Ln--)}function B(e,t){Ln++,pa[Ln]=e.current,e.current=t}var $t={},ye=Gt($t),Te=Gt(!1),pn=$t;function Qn(e,t){var n=e.type.contextTypes;if(!n)return $t;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Re(e){return e=e.childContextTypes,e!=null}function no(){$(Te),$(ye)}function Hu(e,t,n){if(ye.current!==$t)throw Error(P(168));B(ye,t),B(Te,n)}function Ff(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(P(108,ig(e)||"Unknown",i));return Y({},n,r)}function ro(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||$t,pn=ye.current,B(ye,e),B(Te,Te.current),!0}function Ku(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=Ff(e,t,pn),r.__reactInternalMemoizedMergedChildContext=e,$(Te),$(ye),B(ye,e)):$(Te),B(Te,n)}var ct=null,Vo=!1,vs=!1;function Bf(e){ct===null?ct=[e]:ct.push(e)}function Sy(e){Vo=!0,Bf(e)}function Qt(){if(!vs&&ct!==null){vs=!0;var e=0,t=z;try{var n=ct;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}ct=null,Vo=!1}catch(i){throw ct!==null&&(ct=ct.slice(e+1)),df(ol,Qt),i}finally{z=t,vs=!1}}return null}var Nn=[],Mn=0,io=null,oo=0,Ue=[],be=0,hn=null,dt=1,ft="";function en(e,t){Nn[Mn++]=oo,Nn[Mn++]=io,io=e,oo=t}function Uf(e,t,n){Ue[be++]=dt,Ue[be++]=ft,Ue[be++]=hn,hn=e;var r=dt;e=ft;var i=32-Ze(r)-1;r&=~(1<<i),n+=1;var o=32-Ze(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,dt=1<<32-Ze(t)+i|n<<i|r,ft=o+e}else dt=1<<o|n<<i|r,ft=e}function hl(e){e.return!==null&&(en(e,1),Uf(e,1,0))}function ml(e){for(;e===io;)io=Nn[--Mn],Nn[Mn]=null,oo=Nn[--Mn],Nn[Mn]=null;for(;e===hn;)hn=Ue[--be],Ue[be]=null,ft=Ue[--be],Ue[be]=null,dt=Ue[--be],Ue[be]=null}var De=null,Me=null,H=!1,Xe=null;function bf(e,t){var n=$e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Gu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Me=It(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Me=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=hn!==null?{id:dt,overflow:ft}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=$e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Me=null,!0):!1;default:return!1}}function ha(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ma(e){if(H){var t=Me;if(t){var n=t;if(!Gu(e,t)){if(ha(e))throw Error(P(418));t=It(n.nextSibling);var r=De;t&&Gu(e,t)?bf(r,n):(e.flags=e.flags&-4097|2,H=!1,De=e)}}else{if(ha(e))throw Error(P(418));e.flags=e.flags&-4097|2,H=!1,De=e}}}function Qu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function ki(e){if(e!==De)return!1;if(!H)return Qu(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ca(e.type,e.memoizedProps)),t&&(t=Me)){if(ha(e))throw $f(),Error(P(418));for(;t;)bf(e,t),t=It(t.nextSibling)}if(Qu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Me=It(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Me=null}}else Me=De?It(e.stateNode.nextSibling):null;return!0}function $f(){for(var e=Me;e;)e=It(e.nextSibling)}function Yn(){Me=De=null,H=!1}function gl(e){Xe===null?Xe=[e]:Xe.push(e)}var ky=kt.ReactCurrentBatchConfig;function cr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function Ci(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Yu(e){var t=e._init;return t(e._payload)}function Wf(e){function t(m,f){if(e){var h=m.deletions;h===null?(m.deletions=[f],m.flags|=16):h.push(f)}}function n(m,f){if(!e)return null;for(;f!==null;)t(m,f),f=f.sibling;return null}function r(m,f){for(m=new Map;f!==null;)f.key!==null?m.set(f.key,f):m.set(f.index,f),f=f.sibling;return m}function i(m,f){return m=Bt(m,f),m.index=0,m.sibling=null,m}function o(m,f,h){return m.index=h,e?(h=m.alternate,h!==null?(h=h.index,h<f?(m.flags|=2,f):h):(m.flags|=2,f)):(m.flags|=1048576,f)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,f,h,w){return f===null||f.tag!==6?(f=Es(h,m.mode,w),f.return=m,f):(f=i(f,h),f.return=m,f)}function l(m,f,h,w){var k=h.type;return k===En?d(m,f,h.props.children,w,h.key):f!==null&&(f.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Tt&&Yu(k)===f.type)?(w=i(f,h.props),w.ref=cr(m,f,h),w.return=m,w):(w=$i(h.type,h.key,h.props,null,m.mode,w),w.ref=cr(m,f,h),w.return=m,w)}function u(m,f,h,w){return f===null||f.tag!==4||f.stateNode.containerInfo!==h.containerInfo||f.stateNode.implementation!==h.implementation?(f=Ts(h,m.mode,w),f.return=m,f):(f=i(f,h.children||[]),f.return=m,f)}function d(m,f,h,w,k){return f===null||f.tag!==7?(f=dn(h,m.mode,w,k),f.return=m,f):(f=i(f,h),f.return=m,f)}function c(m,f,h){if(typeof f=="string"&&f!==""||typeof f=="number")return f=Es(""+f,m.mode,h),f.return=m,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case fi:return h=$i(f.type,f.key,f.props,null,m.mode,h),h.ref=cr(m,null,f),h.return=m,h;case Pn:return f=Ts(f,m.mode,h),f.return=m,f;case Tt:var w=f._init;return c(m,w(f._payload),h)}if(yr(f)||or(f))return f=dn(f,m.mode,h,null),f.return=m,f;Ci(m,f)}return null}function p(m,f,h,w){var k=f!==null?f.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return k!==null?null:a(m,f,""+h,w);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case fi:return h.key===k?l(m,f,h,w):null;case Pn:return h.key===k?u(m,f,h,w):null;case Tt:return k=h._init,p(m,f,k(h._payload),w)}if(yr(h)||or(h))return k!==null?null:d(m,f,h,w,null);Ci(m,h)}return null}function g(m,f,h,w,k){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(h)||null,a(f,m,""+w,k);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case fi:return m=m.get(w.key===null?h:w.key)||null,l(f,m,w,k);case Pn:return m=m.get(w.key===null?h:w.key)||null,u(f,m,w,k);case Tt:var T=w._init;return g(m,f,h,T(w._payload),k)}if(yr(w)||or(w))return m=m.get(h)||null,d(f,m,w,k,null);Ci(f,w)}return null}function v(m,f,h,w){for(var k=null,T=null,C=f,R=f=0,L=null;C!==null&&R<h.length;R++){C.index>R?(L=C,C=null):L=C.sibling;var M=p(m,C,h[R],w);if(M===null){C===null&&(C=L);break}e&&C&&M.alternate===null&&t(m,C),f=o(M,f,R),T===null?k=M:T.sibling=M,T=M,C=L}if(R===h.length)return n(m,C),H&&en(m,R),k;if(C===null){for(;R<h.length;R++)C=c(m,h[R],w),C!==null&&(f=o(C,f,R),T===null?k=C:T.sibling=C,T=C);return H&&en(m,R),k}for(C=r(m,C);R<h.length;R++)L=g(C,m,R,h[R],w),L!==null&&(e&&L.alternate!==null&&C.delete(L.key===null?R:L.key),f=o(L,f,R),T===null?k=L:T.sibling=L,T=L);return e&&C.forEach(function(oe){return t(m,oe)}),H&&en(m,R),k}function x(m,f,h,w){var k=or(h);if(typeof k!="function")throw Error(P(150));if(h=k.call(h),h==null)throw Error(P(151));for(var T=k=null,C=f,R=f=0,L=null,M=h.next();C!==null&&!M.done;R++,M=h.next()){C.index>R?(L=C,C=null):L=C.sibling;var oe=p(m,C,M.value,w);if(oe===null){C===null&&(C=L);break}e&&C&&oe.alternate===null&&t(m,C),f=o(oe,f,R),T===null?k=oe:T.sibling=oe,T=oe,C=L}if(M.done)return n(m,C),H&&en(m,R),k;if(C===null){for(;!M.done;R++,M=h.next())M=c(m,M.value,w),M!==null&&(f=o(M,f,R),T===null?k=M:T.sibling=M,T=M);return H&&en(m,R),k}for(C=r(m,C);!M.done;R++,M=h.next())M=g(C,m,R,M.value,w),M!==null&&(e&&M.alternate!==null&&C.delete(M.key===null?R:M.key),f=o(M,f,R),T===null?k=M:T.sibling=M,T=M);return e&&C.forEach(function(ue){return t(m,ue)}),H&&en(m,R),k}function S(m,f,h,w){if(typeof h=="object"&&h!==null&&h.type===En&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case fi:e:{for(var k=h.key,T=f;T!==null;){if(T.key===k){if(k=h.type,k===En){if(T.tag===7){n(m,T.sibling),f=i(T,h.props.children),f.return=m,m=f;break e}}else if(T.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Tt&&Yu(k)===T.type){n(m,T.sibling),f=i(T,h.props),f.ref=cr(m,T,h),f.return=m,m=f;break e}n(m,T);break}else t(m,T);T=T.sibling}h.type===En?(f=dn(h.props.children,m.mode,w,h.key),f.return=m,m=f):(w=$i(h.type,h.key,h.props,null,m.mode,w),w.ref=cr(m,f,h),w.return=m,m=w)}return s(m);case Pn:e:{for(T=h.key;f!==null;){if(f.key===T)if(f.tag===4&&f.stateNode.containerInfo===h.containerInfo&&f.stateNode.implementation===h.implementation){n(m,f.sibling),f=i(f,h.children||[]),f.return=m,m=f;break e}else{n(m,f);break}else t(m,f);f=f.sibling}f=Ts(h,m.mode,w),f.return=m,m=f}return s(m);case Tt:return T=h._init,S(m,f,T(h._payload),w)}if(yr(h))return v(m,f,h,w);if(or(h))return x(m,f,h,w);Ci(m,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,f!==null&&f.tag===6?(n(m,f.sibling),f=i(f,h),f.return=m,m=f):(n(m,f),f=Es(h,m.mode,w),f.return=m,m=f),s(m)):n(m,f)}return S}var Xn=Wf(!0),Hf=Wf(!1),so=Gt(null),ao=null,Dn=null,yl=null;function vl(){yl=Dn=ao=null}function xl(e){var t=so.current;$(so),e._currentValue=t}function ga(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Wn(e,t){ao=e,yl=Dn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ee=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(yl!==e)if(e={context:e,memoizedValue:t,next:null},Dn===null){if(ao===null)throw Error(P(308));Dn=e,ao.dependencies={lanes:0,firstContext:e}}else Dn=Dn.next=e;return t}var an=null;function wl(e){an===null?an=[e]:an.push(e)}function Kf(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,wl(t)):(n.next=i.next,i.next=n),t.interleaved=n,xt(e,r)}function xt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Rt=!1;function Sl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Gf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ht(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ot(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,O&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,xt(e,n)}return i=r.interleaved,i===null?(t.next=t,wl(r)):(t.next=i.next,i.next=t),r.interleaved=t,xt(e,n)}function Oi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sl(e,n)}}function Xu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function lo(e,t,n,r){var i=e.updateQueue;Rt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?o=u:s.next=u,s=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==s&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(o!==null){var c=i.baseState;s=0,d=u=l=null,a=o;do{var p=a.lane,g=a.eventTime;if((r&p)===p){d!==null&&(d=d.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(p=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){c=v.call(g,c,p);break e}c=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,p=typeof v=="function"?v.call(g,c,p):v,p==null)break e;c=Y({},c,p);break e;case 2:Rt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[a]:p.push(a))}else g={eventTime:g,lane:p,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=g,l=c):d=d.next=g,s|=p;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;p=a,a=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(d===null&&(l=c),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=d,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);gn|=s,e.lanes=s,e.memoizedState=c}}function Zu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(P(191,i));i.call(r)}}}var ii={},at=Gt(ii),Hr=Gt(ii),Kr=Gt(ii);function ln(e){if(e===ii)throw Error(P(174));return e}function kl(e,t){switch(B(Kr,t),B(Hr,e),B(at,ii),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Xs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Xs(t,e)}$(at),B(at,t)}function Zn(){$(at),$(Hr),$(Kr)}function Qf(e){ln(Kr.current);var t=ln(at.current),n=Xs(t,e.type);t!==n&&(B(Hr,e),B(at,n))}function Cl(e){Hr.current===e&&($(at),$(Hr))}var K=Gt(0);function uo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var xs=[];function Pl(){for(var e=0;e<xs.length;e++)xs[e]._workInProgressVersionPrimary=null;xs.length=0}var zi=kt.ReactCurrentDispatcher,ws=kt.ReactCurrentBatchConfig,mn=0,Q=null,re=null,ae=null,co=!1,Tr=!1,Gr=0,Cy=0;function pe(){throw Error(P(321))}function El(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Je(e[n],t[n]))return!1;return!0}function Tl(e,t,n,r,i,o){if(mn=o,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,zi.current=e===null||e.memoizedState===null?Ry:Ay,e=n(r,i),Tr){o=0;do{if(Tr=!1,Gr=0,25<=o)throw Error(P(301));o+=1,ae=re=null,t.updateQueue=null,zi.current=jy,e=n(r,i)}while(Tr)}if(zi.current=fo,t=re!==null&&re.next!==null,mn=0,ae=re=Q=null,co=!1,t)throw Error(P(300));return e}function Rl(){var e=Gr!==0;return Gr=0,e}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ae===null?Q.memoizedState=ae=e:ae=ae.next=e,ae}function Ke(){if(re===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=ae===null?Q.memoizedState:ae.next;if(t!==null)ae=t,re=e;else{if(e===null)throw Error(P(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},ae===null?Q.memoizedState=ae=e:ae=ae.next=e}return ae}function Qr(e,t){return typeof t=="function"?t(e):t}function Ss(e){var t=Ke(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=re,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,l=null,u=o;do{var d=u.lane;if((mn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=c,s=r):l=l.next=c,Q.lanes|=d,gn|=d}u=u.next}while(u!==null&&u!==o);l===null?s=r:l.next=a,Je(r,t.memoizedState)||(Ee=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Q.lanes|=o,gn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ks(e){var t=Ke(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Je(o,t.memoizedState)||(Ee=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Yf(){}function Xf(e,t){var n=Q,r=Ke(),i=t(),o=!Je(r.memoizedState,i);if(o&&(r.memoizedState=i,Ee=!0),r=r.queue,Al(Jf.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ae!==null&&ae.memoizedState.tag&1){if(n.flags|=2048,Yr(9,qf.bind(null,n,r,i,t),void 0,null),le===null)throw Error(P(349));mn&30||Zf(n,t,i)}return i}function Zf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function qf(e,t,n,r){t.value=n,t.getSnapshot=r,ep(t)&&tp(e)}function Jf(e,t,n){return n(function(){ep(t)&&tp(e)})}function ep(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Je(e,n)}catch{return!0}}function tp(e){var t=xt(e,1);t!==null&&qe(t,e,1,-1)}function qu(e){var t=rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Qr,lastRenderedState:e},t.queue=e,e=e.dispatch=Ty.bind(null,Q,e),[t.memoizedState,e]}function Yr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function np(){return Ke().memoizedState}function Fi(e,t,n,r){var i=rt();Q.flags|=e,i.memoizedState=Yr(1|t,n,void 0,r===void 0?null:r)}function Io(e,t,n,r){var i=Ke();r=r===void 0?null:r;var o=void 0;if(re!==null){var s=re.memoizedState;if(o=s.destroy,r!==null&&El(r,s.deps)){i.memoizedState=Yr(t,n,o,r);return}}Q.flags|=e,i.memoizedState=Yr(1|t,n,o,r)}function Ju(e,t){return Fi(8390656,8,e,t)}function Al(e,t){return Io(2048,8,e,t)}function rp(e,t){return Io(4,2,e,t)}function ip(e,t){return Io(4,4,e,t)}function op(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function sp(e,t,n){return n=n!=null?n.concat([e]):null,Io(4,4,op.bind(null,t,e),n)}function jl(){}function ap(e,t){var n=Ke();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&El(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function lp(e,t){var n=Ke();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&El(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function up(e,t,n){return mn&21?(Je(n,t)||(n=hf(),Q.lanes|=n,gn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ee=!0),e.memoizedState=n)}function Py(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=ws.transition;ws.transition={};try{e(!1),t()}finally{z=n,ws.transition=r}}function cp(){return Ke().memoizedState}function Ey(e,t,n){var r=Ft(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},dp(e))fp(t,n);else if(n=Kf(e,t,n,r),n!==null){var i=Se();qe(n,e,r,i),pp(n,t,r)}}function Ty(e,t,n){var r=Ft(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(dp(e))fp(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,Je(a,s)){var l=t.interleaved;l===null?(i.next=i,wl(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=Kf(e,t,i,r),n!==null&&(i=Se(),qe(n,e,r,i),pp(n,t,r))}}function dp(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function fp(e,t){Tr=co=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function pp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,sl(e,n)}}var fo={readContext:He,useCallback:pe,useContext:pe,useEffect:pe,useImperativeHandle:pe,useInsertionEffect:pe,useLayoutEffect:pe,useMemo:pe,useReducer:pe,useRef:pe,useState:pe,useDebugValue:pe,useDeferredValue:pe,useTransition:pe,useMutableSource:pe,useSyncExternalStore:pe,useId:pe,unstable_isNewReconciler:!1},Ry={readContext:He,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:Ju,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Fi(4194308,4,op.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Fi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Fi(4,2,e,t)},useMemo:function(e,t){var n=rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ey.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:qu,useDebugValue:jl,useDeferredValue:function(e){return rt().memoizedState=e},useTransition:function(){var e=qu(!1),t=e[0];return e=Py.bind(null,e[1]),rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,i=rt();if(H){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),le===null)throw Error(P(349));mn&30||Zf(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Ju(Jf.bind(null,r,o,e),[e]),r.flags|=2048,Yr(9,qf.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=rt(),t=le.identifierPrefix;if(H){var n=ft,r=dt;n=(r&~(1<<32-Ze(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Gr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Cy++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ay={readContext:He,useCallback:ap,useContext:He,useEffect:Al,useImperativeHandle:sp,useInsertionEffect:rp,useLayoutEffect:ip,useMemo:lp,useReducer:Ss,useRef:np,useState:function(){return Ss(Qr)},useDebugValue:jl,useDeferredValue:function(e){var t=Ke();return up(t,re.memoizedState,e)},useTransition:function(){var e=Ss(Qr)[0],t=Ke().memoizedState;return[e,t]},useMutableSource:Yf,useSyncExternalStore:Xf,useId:cp,unstable_isNewReconciler:!1},jy={readContext:He,useCallback:ap,useContext:He,useEffect:Al,useImperativeHandle:sp,useInsertionEffect:rp,useLayoutEffect:ip,useMemo:lp,useReducer:ks,useRef:np,useState:function(){return ks(Qr)},useDebugValue:jl,useDeferredValue:function(e){var t=Ke();return re===null?t.memoizedState=e:up(t,re.memoizedState,e)},useTransition:function(){var e=ks(Qr)[0],t=Ke().memoizedState;return[e,t]},useMutableSource:Yf,useSyncExternalStore:Xf,useId:cp,unstable_isNewReconciler:!1};function Qe(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ya(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Oo={isMounted:function(e){return(e=e._reactInternals)?xn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Se(),i=Ft(e),o=ht(r,i);o.payload=t,n!=null&&(o.callback=n),t=Ot(e,o,i),t!==null&&(qe(t,e,i,r),Oi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Se(),i=Ft(e),o=ht(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Ot(e,o,i),t!==null&&(qe(t,e,i,r),Oi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Se(),r=Ft(e),i=ht(n,r);i.tag=2,t!=null&&(i.callback=t),t=Ot(e,i,r),t!==null&&(qe(t,e,r,n),Oi(t,e,r))}};function ec(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Ur(n,r)||!Ur(i,o):!0}function hp(e,t,n){var r=!1,i=$t,o=t.contextType;return typeof o=="object"&&o!==null?o=He(o):(i=Re(t)?pn:ye.current,r=t.contextTypes,o=(r=r!=null)?Qn(e,i):$t),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Oo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function tc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Oo.enqueueReplaceState(t,t.state,null)}function va(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Sl(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=He(o):(o=Re(t)?pn:ye.current,i.context=Qn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(ya(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Oo.enqueueReplaceState(i,i.state,null),lo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function qn(e,t){try{var n="",r=t;do n+=rg(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Cs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function xa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ly=typeof WeakMap=="function"?WeakMap:Map;function mp(e,t,n){n=ht(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ho||(ho=!0,ja=r),xa(e,t)},n}function gp(e,t,n){n=ht(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){xa(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){xa(e,t),typeof r!="function"&&(zt===null?zt=new Set([this]):zt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function nc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ly;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Wy.bind(null,e,t,n),t.then(e,e))}function rc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ic(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ht(-1,1),t.tag=2,Ot(n,t,1))),n.lanes|=1),e)}var Ny=kt.ReactCurrentOwner,Ee=!1;function we(e,t,n,r){t.child=e===null?Hf(t,null,n,r):Xn(t,e.child,n,r)}function oc(e,t,n,r,i){n=n.render;var o=t.ref;return Wn(t,i),r=Tl(e,t,n,r,o,i),n=Rl(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,wt(e,t,i)):(H&&n&&hl(t),t.flags|=1,we(e,t,r,i),t.child)}function sc(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Ol(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,yp(e,t,o,r,i)):(e=$i(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Ur,n(s,r)&&e.ref===t.ref)return wt(e,t,i)}return t.flags|=1,e=Bt(o,r),e.ref=t.ref,e.return=t,t.child=e}function yp(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Ur(o,r)&&e.ref===t.ref)if(Ee=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ee=!0);else return t.lanes=e.lanes,wt(e,t,i)}return wa(e,t,n,r,i)}function vp(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Vn,Ne),Ne|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Vn,Ne),Ne|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,B(Vn,Ne),Ne|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,B(Vn,Ne),Ne|=r;return we(e,t,i,n),t.child}function xp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function wa(e,t,n,r,i){var o=Re(n)?pn:ye.current;return o=Qn(t,o),Wn(t,i),n=Tl(e,t,n,r,o,i),r=Rl(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,wt(e,t,i)):(H&&r&&hl(t),t.flags|=1,we(e,t,n,i),t.child)}function ac(e,t,n,r,i){if(Re(n)){var o=!0;ro(t)}else o=!1;if(Wn(t,i),t.stateNode===null)Bi(e,t),hp(t,n,r),va(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Re(n)?pn:ye.current,u=Qn(t,u));var d=n.getDerivedStateFromProps,c=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";c||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&tc(t,s,r,u),Rt=!1;var p=t.memoizedState;s.state=p,lo(t,r,s,i),l=t.memoizedState,a!==r||p!==l||Te.current||Rt?(typeof d=="function"&&(ya(t,n,d,r),l=t.memoizedState),(a=Rt||ec(t,n,a,r,p,l,u))?(c||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Gf(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Qe(t.type,a),s.props=u,c=t.pendingProps,p=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=He(l):(l=Re(n)?pn:ye.current,l=Qn(t,l));var g=n.getDerivedStateFromProps;(d=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==c||p!==l)&&tc(t,s,r,l),Rt=!1,p=t.memoizedState,s.state=p,lo(t,r,s,i);var v=t.memoizedState;a!==c||p!==v||Te.current||Rt?(typeof g=="function"&&(ya(t,n,g,r),v=t.memoizedState),(u=Rt||ec(t,n,u,r,p,v,l)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Sa(e,t,n,r,o,i)}function Sa(e,t,n,r,i,o){xp(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Ku(t,n,!1),wt(e,t,o);r=t.stateNode,Ny.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Xn(t,e.child,null,o),t.child=Xn(t,null,a,o)):we(e,t,a,o),t.memoizedState=r.state,i&&Ku(t,n,!0),t.child}function wp(e){var t=e.stateNode;t.pendingContext?Hu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Hu(e,t.context,!1),kl(e,t.containerInfo)}function lc(e,t,n,r,i){return Yn(),gl(i),t.flags|=256,we(e,t,n,r),t.child}var ka={dehydrated:null,treeContext:null,retryLane:0};function Ca(e){return{baseLanes:e,cachePool:null,transitions:null}}function Sp(e,t,n){var r=t.pendingProps,i=K.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),B(K,i&1),e===null)return ma(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Bo(s,r,0,null),e=dn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Ca(n),t.memoizedState=ka,e):Ll(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return My(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Bt(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=Bt(a,o):(o=dn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?Ca(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=ka,r}return o=e.child,e=o.sibling,r=Bt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ll(e,t){return t=Bo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Pi(e,t,n,r){return r!==null&&gl(r),Xn(t,e.child,null,n),e=Ll(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function My(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Cs(Error(P(422))),Pi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Bo({mode:"visible",children:r.children},i,0,null),o=dn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Xn(t,e.child,null,s),t.child.memoizedState=Ca(s),t.memoizedState=ka,o);if(!(t.mode&1))return Pi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(P(419)),r=Cs(o,r,void 0),Pi(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ee||a){if(r=le,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,xt(e,i),qe(r,e,i,-1))}return Il(),r=Cs(Error(P(421))),Pi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Hy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Me=It(i.nextSibling),De=t,H=!0,Xe=null,e!==null&&(Ue[be++]=dt,Ue[be++]=ft,Ue[be++]=hn,dt=e.id,ft=e.overflow,hn=t),t=Ll(t,r.children),t.flags|=4096,t)}function uc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ga(e.return,t,n)}function Ps(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function kp(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(we(e,t,r.children,n),r=K.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&uc(e,n,t);else if(e.tag===19)uc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(K,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&uo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ps(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&uo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ps(t,!0,n,null,o);break;case"together":Ps(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Bi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function wt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),gn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=Bt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Bt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Dy(e,t,n){switch(t.tag){case 3:wp(t),Yn();break;case 5:Qf(t);break;case 1:Re(t.type)&&ro(t);break;case 4:kl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;B(so,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(K,K.current&1),t.flags|=128,null):n&t.child.childLanes?Sp(e,t,n):(B(K,K.current&1),e=wt(e,t,n),e!==null?e.sibling:null);B(K,K.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return kp(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(K,K.current),r)break;return null;case 22:case 23:return t.lanes=0,vp(e,t,n)}return wt(e,t,n)}var Cp,Pa,Pp,Ep;Cp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Pa=function(){};Pp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,ln(at.current);var o=null;switch(n){case"input":i=Ks(e,i),r=Ks(e,r),o=[];break;case"select":i=Y({},i,{value:void 0}),r=Y({},r,{value:void 0}),o=[];break;case"textarea":i=Ys(e,i),r=Ys(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=to)}Zs(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(_r.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(_r.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&b("scroll",e),o||a===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Ep=function(e,t,n,r){n!==r&&(t.flags|=4)};function dr(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function he(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function _y(e,t,n){var r=t.pendingProps;switch(ml(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return he(t),null;case 1:return Re(t.type)&&no(),he(t),null;case 3:return r=t.stateNode,Zn(),$(Te),$(ye),Pl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ki(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Xe!==null&&(Ma(Xe),Xe=null))),Pa(e,t),he(t),null;case 5:Cl(t);var i=ln(Kr.current);if(n=t.type,e!==null&&t.stateNode!=null)Pp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return he(t),null}if(e=ln(at.current),ki(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[it]=t,r[Wr]=o,e=(t.mode&1)!==0,n){case"dialog":b("cancel",r),b("close",r);break;case"iframe":case"object":case"embed":b("load",r);break;case"video":case"audio":for(i=0;i<xr.length;i++)b(xr[i],r);break;case"source":b("error",r);break;case"img":case"image":case"link":b("error",r),b("load",r);break;case"details":b("toggle",r);break;case"input":vu(r,o),b("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},b("invalid",r);break;case"textarea":wu(r,o),b("invalid",r)}Zs(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&Si(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&Si(r.textContent,a,e),i=["children",""+a]):_r.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&b("scroll",r)}switch(n){case"input":pi(r),xu(r,o,!0);break;case"textarea":pi(r),Su(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=to)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=qd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[it]=t,e[Wr]=r,Cp(e,t,!1,!1),t.stateNode=e;e:{switch(s=qs(n,r),n){case"dialog":b("cancel",e),b("close",e),i=r;break;case"iframe":case"object":case"embed":b("load",e),i=r;break;case"video":case"audio":for(i=0;i<xr.length;i++)b(xr[i],e);i=r;break;case"source":b("error",e),i=r;break;case"img":case"image":case"link":b("error",e),b("load",e),i=r;break;case"details":b("toggle",e),i=r;break;case"input":vu(e,r),i=Ks(e,r),b("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Y({},r,{value:void 0}),b("invalid",e);break;case"textarea":wu(e,r),i=Ys(e,r),b("invalid",e);break;default:i=r}Zs(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?tf(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Jd(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Vr(e,l):typeof l=="number"&&Vr(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(_r.hasOwnProperty(o)?l!=null&&o==="onScroll"&&b("scroll",e):l!=null&&el(e,o,l,s))}switch(n){case"input":pi(e),xu(e,r,!1);break;case"textarea":pi(e),Su(e);break;case"option":r.value!=null&&e.setAttribute("value",""+bt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Bn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Bn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=to)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return he(t),null;case 6:if(e&&t.stateNode!=null)Ep(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=ln(Kr.current),ln(at.current),ki(t)){if(r=t.stateNode,n=t.memoizedProps,r[it]=t,(o=r.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:Si(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Si(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[it]=t,t.stateNode=r}return he(t),null;case 13:if($(K),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Me!==null&&t.mode&1&&!(t.flags&128))$f(),Yn(),t.flags|=98560,o=!1;else if(o=ki(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(P(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(P(317));o[it]=t}else Yn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;he(t),o=!1}else Xe!==null&&(Ma(Xe),Xe=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||K.current&1?ie===0&&(ie=3):Il())),t.updateQueue!==null&&(t.flags|=4),he(t),null);case 4:return Zn(),Pa(e,t),e===null&&br(t.stateNode.containerInfo),he(t),null;case 10:return xl(t.type._context),he(t),null;case 17:return Re(t.type)&&no(),he(t),null;case 19:if($(K),o=t.memoizedState,o===null)return he(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)dr(o,!1);else{if(ie!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=uo(e),s!==null){for(t.flags|=128,dr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(K,K.current&1|2),t.child}e=e.sibling}o.tail!==null&&q()>Jn&&(t.flags|=128,r=!0,dr(o,!1),t.lanes=4194304)}else{if(!r)if(e=uo(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),dr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return he(t),null}else 2*q()-o.renderingStartTime>Jn&&n!==1073741824&&(t.flags|=128,r=!0,dr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=q(),t.sibling=null,n=K.current,B(K,r?n&1|2:n&1),t):(he(t),null);case 22:case 23:return Vl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ne&1073741824&&(he(t),t.subtreeFlags&6&&(t.flags|=8192)):he(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function Vy(e,t){switch(ml(t),t.tag){case 1:return Re(t.type)&&no(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Zn(),$(Te),$(ye),Pl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Cl(t),null;case 13:if($(K),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));Yn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(K),null;case 4:return Zn(),null;case 10:return xl(t.type._context),null;case 22:case 23:return Vl(),null;case 24:return null;default:return null}}var Ei=!1,ge=!1,Iy=typeof WeakSet=="function"?WeakSet:Set,j=null;function _n(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function Ea(e,t,n){try{n()}catch(r){X(e,t,r)}}var cc=!1;function Oy(e,t){if(la=qi,e=Lf(),pl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,d=0,c=e,p=null;t:for(;;){for(var g;c!==n||i!==0&&c.nodeType!==3||(a=s+i),c!==o||r!==0&&c.nodeType!==3||(l=s+r),c.nodeType===3&&(s+=c.nodeValue.length),(g=c.firstChild)!==null;)p=c,c=g;for(;;){if(c===e)break t;if(p===n&&++u===i&&(a=s),p===o&&++d===r&&(l=s),(g=c.nextSibling)!==null)break;c=p,p=c.parentNode}c=g}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(ua={focusedElem:e,selectionRange:n},qi=!1,j=t;j!==null;)if(t=j,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,j=e;else for(;j!==null;){t=j;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,S=v.memoizedState,m=t.stateNode,f=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:Qe(t.type,x),S);m.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(w){X(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,j=e;break}j=t.return}return v=cc,cc=!1,v}function Rr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Ea(t,n,o)}i=i.next}while(i!==r)}}function zo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ta(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Tp(e){var t=e.alternate;t!==null&&(e.alternate=null,Tp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[it],delete t[Wr],delete t[fa],delete t[xy],delete t[wy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Rp(e){return e.tag===5||e.tag===3||e.tag===4}function dc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Rp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ra(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=to));else if(r!==4&&(e=e.child,e!==null))for(Ra(e,t,n),e=e.sibling;e!==null;)Ra(e,t,n),e=e.sibling}function Aa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Aa(e,t,n),e=e.sibling;e!==null;)Aa(e,t,n),e=e.sibling}var ce=null,Ye=!1;function Pt(e,t,n){for(n=n.child;n!==null;)Ap(e,t,n),n=n.sibling}function Ap(e,t,n){if(st&&typeof st.onCommitFiberUnmount=="function")try{st.onCommitFiberUnmount(Lo,n)}catch{}switch(n.tag){case 5:ge||_n(n,t);case 6:var r=ce,i=Ye;ce=null,Pt(e,t,n),ce=r,Ye=i,ce!==null&&(Ye?(e=ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ce.removeChild(n.stateNode));break;case 18:ce!==null&&(Ye?(e=ce,n=n.stateNode,e.nodeType===8?ys(e.parentNode,n):e.nodeType===1&&ys(e,n),Fr(e)):ys(ce,n.stateNode));break;case 4:r=ce,i=Ye,ce=n.stateNode.containerInfo,Ye=!0,Pt(e,t,n),ce=r,Ye=i;break;case 0:case 11:case 14:case 15:if(!ge&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Ea(n,t,s),i=i.next}while(i!==r)}Pt(e,t,n);break;case 1:if(!ge&&(_n(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){X(n,t,a)}Pt(e,t,n);break;case 21:Pt(e,t,n);break;case 22:n.mode&1?(ge=(r=ge)||n.memoizedState!==null,Pt(e,t,n),ge=r):Pt(e,t,n);break;default:Pt(e,t,n)}}function fc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Iy),t.forEach(function(r){var i=Ky.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ge(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:ce=a.stateNode,Ye=!1;break e;case 3:ce=a.stateNode.containerInfo,Ye=!0;break e;case 4:ce=a.stateNode.containerInfo,Ye=!0;break e}a=a.return}if(ce===null)throw Error(P(160));Ap(o,s,i),ce=null,Ye=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){X(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)jp(t,e),t=t.sibling}function jp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ge(t,e),nt(e),r&4){try{Rr(3,e,e.return),zo(3,e)}catch(x){X(e,e.return,x)}try{Rr(5,e,e.return)}catch(x){X(e,e.return,x)}}break;case 1:Ge(t,e),nt(e),r&512&&n!==null&&_n(n,n.return);break;case 5:if(Ge(t,e),nt(e),r&512&&n!==null&&_n(n,n.return),e.flags&32){var i=e.stateNode;try{Vr(i,"")}catch(x){X(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&Xd(i,o),qs(a,s);var u=qs(a,o);for(s=0;s<l.length;s+=2){var d=l[s],c=l[s+1];d==="style"?tf(i,c):d==="dangerouslySetInnerHTML"?Jd(i,c):d==="children"?Vr(i,c):el(i,d,c,u)}switch(a){case"input":Gs(i,o);break;case"textarea":Zd(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?Bn(i,!!o.multiple,g,!1):p!==!!o.multiple&&(o.defaultValue!=null?Bn(i,!!o.multiple,o.defaultValue,!0):Bn(i,!!o.multiple,o.multiple?[]:"",!1))}i[Wr]=o}catch(x){X(e,e.return,x)}}break;case 6:if(Ge(t,e),nt(e),r&4){if(e.stateNode===null)throw Error(P(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(x){X(e,e.return,x)}}break;case 3:if(Ge(t,e),nt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Fr(t.containerInfo)}catch(x){X(e,e.return,x)}break;case 4:Ge(t,e),nt(e);break;case 13:Ge(t,e),nt(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Dl=q())),r&4&&fc(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ge=(u=ge)||d,Ge(t,e),ge=u):Ge(t,e),nt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(j=e,d=e.child;d!==null;){for(c=j=d;j!==null;){switch(p=j,g=p.child,p.tag){case 0:case 11:case 14:case 15:Rr(4,p,p.return);break;case 1:_n(p,p.return);var v=p.stateNode;if(typeof v.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){X(r,n,x)}}break;case 5:_n(p,p.return);break;case 22:if(p.memoizedState!==null){hc(c);continue}}g!==null?(g.return=p,j=g):hc(c)}d=d.sibling}e:for(d=null,c=e;;){if(c.tag===5){if(d===null){d=c;try{i=c.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=c.stateNode,l=c.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=ef("display",s))}catch(x){X(e,e.return,x)}}}else if(c.tag===6){if(d===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(x){X(e,e.return,x)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;d===c&&(d=null),c=c.return}d===c&&(d=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:Ge(t,e),nt(e),r&4&&fc(e);break;case 21:break;default:Ge(t,e),nt(e)}}function nt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Rp(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Vr(i,""),r.flags&=-33);var o=dc(e);Aa(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=dc(e);Ra(e,a,s);break;default:throw Error(P(161))}}catch(l){X(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function zy(e,t,n){j=e,Lp(e)}function Lp(e,t,n){for(var r=(e.mode&1)!==0;j!==null;){var i=j,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Ei;if(!s){var a=i.alternate,l=a!==null&&a.memoizedState!==null||ge;a=Ei;var u=ge;if(Ei=s,(ge=l)&&!u)for(j=i;j!==null;)s=j,l=s.child,s.tag===22&&s.memoizedState!==null?mc(i):l!==null?(l.return=s,j=l):mc(i);for(;o!==null;)j=o,Lp(o),o=o.sibling;j=i,Ei=a,ge=u}pc(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,j=o):pc(e)}}function pc(e){for(;j!==null;){var t=j;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ge||zo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ge)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Qe(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Zu(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Zu(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var c=d.dehydrated;c!==null&&Fr(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}ge||t.flags&512&&Ta(t)}catch(p){X(t,t.return,p)}}if(t===e){j=null;break}if(n=t.sibling,n!==null){n.return=t.return,j=n;break}j=t.return}}function hc(e){for(;j!==null;){var t=j;if(t===e){j=null;break}var n=t.sibling;if(n!==null){n.return=t.return,j=n;break}j=t.return}}function mc(e){for(;j!==null;){var t=j;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{zo(4,t)}catch(l){X(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){X(t,i,l)}}var o=t.return;try{Ta(t)}catch(l){X(t,o,l)}break;case 5:var s=t.return;try{Ta(t)}catch(l){X(t,s,l)}}}catch(l){X(t,t.return,l)}if(t===e){j=null;break}var a=t.sibling;if(a!==null){a.return=t.return,j=a;break}j=t.return}}var Fy=Math.ceil,po=kt.ReactCurrentDispatcher,Nl=kt.ReactCurrentOwner,We=kt.ReactCurrentBatchConfig,O=0,le=null,te=null,de=0,Ne=0,Vn=Gt(0),ie=0,Xr=null,gn=0,Fo=0,Ml=0,Ar=null,Pe=null,Dl=0,Jn=1/0,ut=null,ho=!1,ja=null,zt=null,Ti=!1,Mt=null,mo=0,jr=0,La=null,Ui=-1,bi=0;function Se(){return O&6?q():Ui!==-1?Ui:Ui=q()}function Ft(e){return e.mode&1?O&2&&de!==0?de&-de:ky.transition!==null?(bi===0&&(bi=hf()),bi):(e=z,e!==0||(e=window.event,e=e===void 0?16:Sf(e.type)),e):1}function qe(e,t,n,r){if(50<jr)throw jr=0,La=null,Error(P(185));ti(e,n,r),(!(O&2)||e!==le)&&(e===le&&(!(O&2)&&(Fo|=n),ie===4&&Lt(e,de)),Ae(e,r),n===1&&O===0&&!(t.mode&1)&&(Jn=q()+500,Vo&&Qt()))}function Ae(e,t){var n=e.callbackNode;kg(e,t);var r=Zi(e,e===le?de:0);if(r===0)n!==null&&Pu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Pu(n),t===1)e.tag===0?Sy(gc.bind(null,e)):Bf(gc.bind(null,e)),yy(function(){!(O&6)&&Qt()}),n=null;else{switch(mf(r)){case 1:n=ol;break;case 4:n=ff;break;case 16:n=Xi;break;case 536870912:n=pf;break;default:n=Xi}n=zp(n,Np.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Np(e,t){if(Ui=-1,bi=0,O&6)throw Error(P(327));var n=e.callbackNode;if(Hn()&&e.callbackNode!==n)return null;var r=Zi(e,e===le?de:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=go(e,r);else{t=r;var i=O;O|=2;var o=Dp();(le!==e||de!==t)&&(ut=null,Jn=q()+500,cn(e,t));do try{by();break}catch(a){Mp(e,a)}while(!0);vl(),po.current=o,O=i,te!==null?t=0:(le=null,de=0,t=ie)}if(t!==0){if(t===2&&(i=ra(e),i!==0&&(r=i,t=Na(e,i))),t===1)throw n=Xr,cn(e,0),Lt(e,r),Ae(e,q()),n;if(t===6)Lt(e,r);else{if(i=e.current.alternate,!(r&30)&&!By(i)&&(t=go(e,r),t===2&&(o=ra(e),o!==0&&(r=o,t=Na(e,o))),t===1))throw n=Xr,cn(e,0),Lt(e,r),Ae(e,q()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:tn(e,Pe,ut);break;case 3:if(Lt(e,r),(r&130023424)===r&&(t=Dl+500-q(),10<t)){if(Zi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Se(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=da(tn.bind(null,e,Pe,ut),t);break}tn(e,Pe,ut);break;case 4:if(Lt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ze(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Fy(r/1960))-r,10<r){e.timeoutHandle=da(tn.bind(null,e,Pe,ut),r);break}tn(e,Pe,ut);break;case 5:tn(e,Pe,ut);break;default:throw Error(P(329))}}}return Ae(e,q()),e.callbackNode===n?Np.bind(null,e):null}function Na(e,t){var n=Ar;return e.current.memoizedState.isDehydrated&&(cn(e,t).flags|=256),e=go(e,t),e!==2&&(t=Pe,Pe=n,t!==null&&Ma(t)),e}function Ma(e){Pe===null?Pe=e:Pe.push.apply(Pe,e)}function By(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Je(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Lt(e,t){for(t&=~Ml,t&=~Fo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ze(t),r=1<<n;e[n]=-1,t&=~r}}function gc(e){if(O&6)throw Error(P(327));Hn();var t=Zi(e,0);if(!(t&1))return Ae(e,q()),null;var n=go(e,t);if(e.tag!==0&&n===2){var r=ra(e);r!==0&&(t=r,n=Na(e,r))}if(n===1)throw n=Xr,cn(e,0),Lt(e,t),Ae(e,q()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,tn(e,Pe,ut),Ae(e,q()),null}function _l(e,t){var n=O;O|=1;try{return e(t)}finally{O=n,O===0&&(Jn=q()+500,Vo&&Qt())}}function yn(e){Mt!==null&&Mt.tag===0&&!(O&6)&&Hn();var t=O;O|=1;var n=We.transition,r=z;try{if(We.transition=null,z=1,e)return e()}finally{z=r,We.transition=n,O=t,!(O&6)&&Qt()}}function Vl(){Ne=Vn.current,$(Vn)}function cn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,gy(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(ml(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&no();break;case 3:Zn(),$(Te),$(ye),Pl();break;case 5:Cl(r);break;case 4:Zn();break;case 13:$(K);break;case 19:$(K);break;case 10:xl(r.type._context);break;case 22:case 23:Vl()}n=n.return}if(le=e,te=e=Bt(e.current,null),de=Ne=t,ie=0,Xr=null,Ml=Fo=gn=0,Pe=Ar=null,an!==null){for(t=0;t<an.length;t++)if(n=an[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}an=null}return e}function Mp(e,t){do{var n=te;try{if(vl(),zi.current=fo,co){for(var r=Q.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}co=!1}if(mn=0,ae=re=Q=null,Tr=!1,Gr=0,Nl.current=null,n===null||n.return===null){ie=1,Xr=t,te=null;break}e:{var o=e,s=n.return,a=n,l=t;if(t=de,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,c=d.tag;if(!(d.mode&1)&&(c===0||c===11||c===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var g=rc(s);if(g!==null){g.flags&=-257,ic(g,s,a,o,t),g.mode&1&&nc(o,u,t),t=g,l=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){nc(o,u,t),Il();break e}l=Error(P(426))}}else if(H&&a.mode&1){var S=rc(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),ic(S,s,a,o,t),gl(qn(l,a));break e}}o=l=qn(l,a),ie!==4&&(ie=2),Ar===null?Ar=[o]:Ar.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=mp(o,l,t);Xu(o,m);break e;case 1:a=l;var f=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof f.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(zt===null||!zt.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=gp(o,a,t);Xu(o,w);break e}}o=o.return}while(o!==null)}Vp(n)}catch(k){t=k,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function Dp(){var e=po.current;return po.current=fo,e===null?fo:e}function Il(){(ie===0||ie===3||ie===2)&&(ie=4),le===null||!(gn&268435455)&&!(Fo&268435455)||Lt(le,de)}function go(e,t){var n=O;O|=2;var r=Dp();(le!==e||de!==t)&&(ut=null,cn(e,t));do try{Uy();break}catch(i){Mp(e,i)}while(!0);if(vl(),O=n,po.current=r,te!==null)throw Error(P(261));return le=null,de=0,ie}function Uy(){for(;te!==null;)_p(te)}function by(){for(;te!==null&&!pg();)_p(te)}function _p(e){var t=Op(e.alternate,e,Ne);e.memoizedProps=e.pendingProps,t===null?Vp(e):te=t,Nl.current=null}function Vp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Vy(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ie=6,te=null;return}}else if(n=_y(n,t,Ne),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);ie===0&&(ie=5)}function tn(e,t,n){var r=z,i=We.transition;try{We.transition=null,z=1,$y(e,t,n,r)}finally{We.transition=i,z=r}return null}function $y(e,t,n,r){do Hn();while(Mt!==null);if(O&6)throw Error(P(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Cg(e,o),e===le&&(te=le=null,de=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ti||(Ti=!0,zp(Xi,function(){return Hn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=We.transition,We.transition=null;var s=z;z=1;var a=O;O|=4,Nl.current=null,Oy(e,n),jp(n,e),uy(ua),qi=!!la,ua=la=null,e.current=n,zy(n),hg(),O=a,z=s,We.transition=o}else e.current=n;if(Ti&&(Ti=!1,Mt=e,mo=i),o=e.pendingLanes,o===0&&(zt=null),yg(n.stateNode),Ae(e,q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ho)throw ho=!1,e=ja,ja=null,e;return mo&1&&e.tag!==0&&Hn(),o=e.pendingLanes,o&1?e===La?jr++:(jr=0,La=e):jr=0,Qt(),null}function Hn(){if(Mt!==null){var e=mf(mo),t=We.transition,n=z;try{if(We.transition=null,z=16>e?16:e,Mt===null)var r=!1;else{if(e=Mt,Mt=null,mo=0,O&6)throw Error(P(331));var i=O;for(O|=4,j=e.current;j!==null;){var o=j,s=o.child;if(j.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(j=u;j!==null;){var d=j;switch(d.tag){case 0:case 11:case 15:Rr(8,d,o)}var c=d.child;if(c!==null)c.return=d,j=c;else for(;j!==null;){d=j;var p=d.sibling,g=d.return;if(Tp(d),d===u){j=null;break}if(p!==null){p.return=g,j=p;break}j=g}}}var v=o.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}j=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,j=s;else e:for(;j!==null;){if(o=j,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Rr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,j=m;break e}j=o.return}}var f=e.current;for(j=f;j!==null;){s=j;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,j=h;else e:for(s=f;j!==null;){if(a=j,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:zo(9,a)}}catch(k){X(a,a.return,k)}if(a===s){j=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,j=w;break e}j=a.return}}if(O=i,Qt(),st&&typeof st.onPostCommitFiberRoot=="function")try{st.onPostCommitFiberRoot(Lo,e)}catch{}r=!0}return r}finally{z=n,We.transition=t}}return!1}function yc(e,t,n){t=qn(n,t),t=mp(e,t,1),e=Ot(e,t,1),t=Se(),e!==null&&(ti(e,1,t),Ae(e,t))}function X(e,t,n){if(e.tag===3)yc(e,e,n);else for(;t!==null;){if(t.tag===3){yc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(zt===null||!zt.has(r))){e=qn(n,e),e=gp(t,e,1),t=Ot(t,e,1),e=Se(),t!==null&&(ti(t,1,e),Ae(t,e));break}}t=t.return}}function Wy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Se(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(de&n)===n&&(ie===4||ie===3&&(de&130023424)===de&&500>q()-Dl?cn(e,0):Ml|=n),Ae(e,t)}function Ip(e,t){t===0&&(e.mode&1?(t=gi,gi<<=1,!(gi&130023424)&&(gi=4194304)):t=1);var n=Se();e=xt(e,t),e!==null&&(ti(e,t,n),Ae(e,n))}function Hy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Ip(e,n)}function Ky(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Ip(e,n)}var Op;Op=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Te.current)Ee=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ee=!1,Dy(e,t,n);Ee=!!(e.flags&131072)}else Ee=!1,H&&t.flags&1048576&&Uf(t,oo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Bi(e,t),e=t.pendingProps;var i=Qn(t,ye.current);Wn(t,n),i=Tl(null,t,r,e,i,n);var o=Rl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Re(r)?(o=!0,ro(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Sl(t),i.updater=Oo,t.stateNode=i,i._reactInternals=t,va(t,r,e,n),t=Sa(null,t,r,!0,o,n)):(t.tag=0,H&&o&&hl(t),we(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Bi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Qy(r),e=Qe(r,e),i){case 0:t=wa(null,t,r,e,n);break e;case 1:t=ac(null,t,r,e,n);break e;case 11:t=oc(null,t,r,e,n);break e;case 14:t=sc(null,t,r,Qe(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),wa(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),ac(e,t,r,i,n);case 3:e:{if(wp(t),e===null)throw Error(P(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Gf(e,t),lo(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=qn(Error(P(423)),t),t=lc(e,t,r,n,i);break e}else if(r!==i){i=qn(Error(P(424)),t),t=lc(e,t,r,n,i);break e}else for(Me=It(t.stateNode.containerInfo.firstChild),De=t,H=!0,Xe=null,n=Hf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Yn(),r===i){t=wt(e,t,n);break e}we(e,t,r,n)}t=t.child}return t;case 5:return Qf(t),e===null&&ma(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,ca(r,i)?s=null:o!==null&&ca(r,o)&&(t.flags|=32),xp(e,t),we(e,t,s,n),t.child;case 6:return e===null&&ma(t),null;case 13:return Sp(e,t,n);case 4:return kl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Xn(t,null,r,n):we(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),oc(e,t,r,i,n);case 7:return we(e,t,t.pendingProps,n),t.child;case 8:return we(e,t,t.pendingProps.children,n),t.child;case 12:return we(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,B(so,r._currentValue),r._currentValue=s,o!==null)if(Je(o.value,s)){if(o.children===i.children&&!Te.current){t=wt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=ht(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),ga(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(P(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),ga(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}we(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Wn(t,n),i=He(i),r=r(i),t.flags|=1,we(e,t,r,n),t.child;case 14:return r=t.type,i=Qe(r,t.pendingProps),i=Qe(r.type,i),sc(e,t,r,i,n);case 15:return yp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Qe(r,i),Bi(e,t),t.tag=1,Re(r)?(e=!0,ro(t)):e=!1,Wn(t,n),hp(t,r,i),va(t,r,i,n),Sa(null,t,r,!0,e,n);case 19:return kp(e,t,n);case 22:return vp(e,t,n)}throw Error(P(156,t.tag))};function zp(e,t){return df(e,t)}function Gy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $e(e,t,n,r){return new Gy(e,t,n,r)}function Ol(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Qy(e){if(typeof e=="function")return Ol(e)?1:0;if(e!=null){if(e=e.$$typeof,e===nl)return 11;if(e===rl)return 14}return 2}function Bt(e,t){var n=e.alternate;return n===null?(n=$e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function $i(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Ol(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case En:return dn(n.children,i,o,t);case tl:s=8,i|=8;break;case bs:return e=$e(12,n,t,i|2),e.elementType=bs,e.lanes=o,e;case $s:return e=$e(13,n,t,i),e.elementType=$s,e.lanes=o,e;case Ws:return e=$e(19,n,t,i),e.elementType=Ws,e.lanes=o,e;case Gd:return Bo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Hd:s=10;break e;case Kd:s=9;break e;case nl:s=11;break e;case rl:s=14;break e;case Tt:s=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=$e(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function dn(e,t,n,r){return e=$e(7,e,r,t),e.lanes=n,e}function Bo(e,t,n,r){return e=$e(22,e,r,t),e.elementType=Gd,e.lanes=n,e.stateNode={isHidden:!1},e}function Es(e,t,n){return e=$e(6,e,null,t),e.lanes=n,e}function Ts(e,t,n){return t=$e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Yy(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ss(0),this.expirationTimes=ss(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ss(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function zl(e,t,n,r,i,o,s,a,l){return e=new Yy(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=$e(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Sl(o),e}function Xy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Pn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Fp(e){if(!e)return $t;e=e._reactInternals;e:{if(xn(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Re(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(Re(n))return Ff(e,n,t)}return t}function Bp(e,t,n,r,i,o,s,a,l){return e=zl(n,r,!0,e,i,o,s,a,l),e.context=Fp(null),n=e.current,r=Se(),i=Ft(n),o=ht(r,i),o.callback=t??null,Ot(n,o,i),e.current.lanes=i,ti(e,i,r),Ae(e,r),e}function Uo(e,t,n,r){var i=t.current,o=Se(),s=Ft(i);return n=Fp(n),t.context===null?t.context=n:t.pendingContext=n,t=ht(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ot(i,t,s),e!==null&&(qe(e,i,s,o),Oi(e,i,s)),s}function yo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function vc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Fl(e,t){vc(e,t),(e=e.alternate)&&vc(e,t)}function Zy(){return null}var Up=typeof reportError=="function"?reportError:function(e){console.error(e)};function Bl(e){this._internalRoot=e}bo.prototype.render=Bl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));Uo(e,t,null,null)};bo.prototype.unmount=Bl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;yn(function(){Uo(null,e,null,null)}),t[vt]=null}};function bo(e){this._internalRoot=e}bo.prototype.unstable_scheduleHydration=function(e){if(e){var t=vf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<jt.length&&t!==0&&t<jt[n].priority;n++);jt.splice(n,0,e),n===0&&wf(e)}};function Ul(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function $o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function xc(){}function qy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=yo(s);o.call(u)}}var s=Bp(t,r,e,0,null,!1,!1,"",xc);return e._reactRootContainer=s,e[vt]=s.current,br(e.nodeType===8?e.parentNode:e),yn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=yo(l);a.call(u)}}var l=zl(e,0,!1,null,null,!1,!1,"",xc);return e._reactRootContainer=l,e[vt]=l.current,br(e.nodeType===8?e.parentNode:e),yn(function(){Uo(t,l,n,r)}),l}function Wo(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var l=yo(s);a.call(l)}}Uo(t,s,e,i)}else s=qy(n,t,e,i,r);return yo(s)}gf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=vr(t.pendingLanes);n!==0&&(sl(t,n|1),Ae(t,q()),!(O&6)&&(Jn=q()+500,Qt()))}break;case 13:yn(function(){var r=xt(e,1);if(r!==null){var i=Se();qe(r,e,1,i)}}),Fl(e,1)}};al=function(e){if(e.tag===13){var t=xt(e,134217728);if(t!==null){var n=Se();qe(t,e,134217728,n)}Fl(e,134217728)}};yf=function(e){if(e.tag===13){var t=Ft(e),n=xt(e,t);if(n!==null){var r=Se();qe(n,e,t,r)}Fl(e,t)}};vf=function(){return z};xf=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};ea=function(e,t,n){switch(t){case"input":if(Gs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=_o(r);if(!i)throw Error(P(90));Yd(r),Gs(r,i)}}}break;case"textarea":Zd(e,n);break;case"select":t=n.value,t!=null&&Bn(e,!!n.multiple,t,!1)}};of=_l;sf=yn;var Jy={usingClientEntryPoint:!1,Events:[ri,jn,_o,nf,rf,_l]},fr={findFiberByHostInstance:sn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ev={bundleType:fr.bundleType,version:fr.version,rendererPackageName:fr.rendererPackageName,rendererConfig:fr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:kt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=uf(e),e===null?null:e.stateNode},findFiberByHostInstance:fr.findFiberByHostInstance||Zy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ri=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ri.isDisabled&&Ri.supportsFiber)try{Lo=Ri.inject(ev),st=Ri}catch{}}Ie.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Jy;Ie.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ul(t))throw Error(P(200));return Xy(e,t,null,n)};Ie.createRoot=function(e,t){if(!Ul(e))throw Error(P(299));var n=!1,r="",i=Up;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=zl(e,1,!1,null,null,n,!1,r,i),e[vt]=t.current,br(e.nodeType===8?e.parentNode:e),new Bl(t)};Ie.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=uf(t),e=e===null?null:e.stateNode,e};Ie.flushSync=function(e){return yn(e)};Ie.hydrate=function(e,t,n){if(!$o(t))throw Error(P(200));return Wo(null,e,t,!0,n)};Ie.hydrateRoot=function(e,t,n){if(!Ul(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=Up;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Bp(t,null,e,1,n??null,i,!1,o,s),e[vt]=t.current,br(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new bo(t)};Ie.render=function(e,t,n){if(!$o(t))throw Error(P(200));return Wo(null,e,t,!1,n)};Ie.unmountComponentAtNode=function(e){if(!$o(e))throw Error(P(40));return e._reactRootContainer?(yn(function(){Wo(null,null,e,!1,function(){e._reactRootContainer=null,e[vt]=null})}),!0):!1};Ie.unstable_batchedUpdates=_l;Ie.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!$o(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return Wo(e,t,n,!1,r)};Ie.version="18.3.1-next-f1338f8080-20240426";function bp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(bp)}catch(e){console.error(e)}}bp(),Ud.exports=Ie;var tv=Ud.exports,wc=tv;Bs.createRoot=wc.createRoot,Bs.hydrateRoot=wc.hydrateRoot;const nv={azureOpenAI:{endpoint:"",apiKey:"",model:"gpt-4o-mini-audio-preview",transcribeModel:"gpt-4o-mini-transcribe"},azureSpeech:{region:"eastus",apiKey:""},hotkeys:{aiMode:"CommandOrControl+Shift+C",directMode:"CommandOrControl+Shift+V"},recording:{deviceId:"default",hotkeyMode:"toggle"},ui:{theme:"light",language:"zh-TW"}},pr={sampleRate:16e3,channels:1,bitsPerSample:16,format:"wav",maxDuration:300,minDuration:.5};var bl={exports:{}},Kn=typeof Reflect=="object"?Reflect:null,Sc=Kn&&typeof Kn.apply=="function"?Kn.apply:function(t,n,r){return Function.prototype.apply.call(t,n,r)},Wi;Kn&&typeof Kn.ownKeys=="function"?Wi=Kn.ownKeys:Object.getOwnPropertySymbols?Wi=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:Wi=function(t){return Object.getOwnPropertyNames(t)};function rv(e){console&&console.warn&&console.warn(e)}var $p=Number.isNaN||function(t){return t!==t};function F(){F.init.call(this)}bl.exports=F;bl.exports.once=av;F.EventEmitter=F;F.prototype._events=void 0;F.prototype._eventsCount=0;F.prototype._maxListeners=void 0;var kc=10;function Ho(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(F,"defaultMaxListeners",{enumerable:!0,get:function(){return kc},set:function(e){if(typeof e!="number"||e<0||$p(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");kc=e}});F.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};F.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||$p(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function Wp(e){return e._maxListeners===void 0?F.defaultMaxListeners:e._maxListeners}F.prototype.getMaxListeners=function(){return Wp(this)};F.prototype.emit=function(t){for(var n=[],r=1;r<arguments.length;r++)n.push(arguments[r]);var i=t==="error",o=this._events;if(o!==void 0)i=i&&o.error===void 0;else if(!i)return!1;if(i){var s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;var a=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var l=o[t];if(l===void 0)return!1;if(typeof l=="function")Sc(l,this,n);else for(var u=l.length,d=Yp(l,u),r=0;r<u;++r)Sc(d[r],this,n);return!0};function Hp(e,t,n,r){var i,o,s;if(Ho(n),o=e._events,o===void 0?(o=e._events=Object.create(null),e._eventsCount=0):(o.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),o=e._events),s=o[t]),s===void 0)s=o[t]=n,++e._eventsCount;else if(typeof s=="function"?s=o[t]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),i=Wp(e),i>0&&s.length>i&&!s.warned){s.warned=!0;var a=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");a.name="MaxListenersExceededWarning",a.emitter=e,a.type=t,a.count=s.length,rv(a)}return e}F.prototype.addListener=function(t,n){return Hp(this,t,n,!1)};F.prototype.on=F.prototype.addListener;F.prototype.prependListener=function(t,n){return Hp(this,t,n,!0)};function iv(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Kp(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=iv.bind(r);return i.listener=n,r.wrapFn=i,i}F.prototype.once=function(t,n){return Ho(n),this.on(t,Kp(this,t,n)),this};F.prototype.prependOnceListener=function(t,n){return Ho(n),this.prependListener(t,Kp(this,t,n)),this};F.prototype.removeListener=function(t,n){var r,i,o,s,a;if(Ho(n),i=this._events,i===void 0)return this;if(r=i[t],r===void 0)return this;if(r===n||r.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete i[t],i.removeListener&&this.emit("removeListener",t,r.listener||n));else if(typeof r!="function"){for(o=-1,s=r.length-1;s>=0;s--)if(r[s]===n||r[s].listener===n){a=r[s].listener,o=s;break}if(o<0)return this;o===0?r.shift():ov(r,o),r.length===1&&(i[t]=r[0]),i.removeListener!==void 0&&this.emit("removeListener",t,a||n)}return this};F.prototype.off=F.prototype.removeListener;F.prototype.removeAllListeners=function(t){var n,r,i;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var o=Object.keys(r),s;for(i=0;i<o.length;++i)s=o[i],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=r[t],typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(i=n.length-1;i>=0;i--)this.removeListener(t,n[i]);return this};function Gp(e,t,n){var r=e._events;if(r===void 0)return[];var i=r[t];return i===void 0?[]:typeof i=="function"?n?[i.listener||i]:[i]:n?sv(i):Yp(i,i.length)}F.prototype.listeners=function(t){return Gp(this,t,!0)};F.prototype.rawListeners=function(t){return Gp(this,t,!1)};F.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):Qp.call(e,t)};F.prototype.listenerCount=Qp;function Qp(e){var t=this._events;if(t!==void 0){var n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}F.prototype.eventNames=function(){return this._eventsCount>0?Wi(this._events):[]};function Yp(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function ov(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function sv(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function av(e,t){return new Promise(function(n,r){function i(s){e.removeListener(t,o),r(s)}function o(){typeof e.removeListener=="function"&&e.removeListener("error",i),n([].slice.call(arguments))}Xp(e,t,o,{once:!0}),t!=="error"&&lv(e,i,{once:!0})})}function lv(e,t,n){typeof e.on=="function"&&Xp(e,"error",t,n)}function Xp(e,t,n,r){if(typeof e.on=="function")r.once?e.once(t,n):e.on(t,n);else if(typeof e.addEventListener=="function")e.addEventListener(t,function i(o){r.once&&e.removeEventListener(t,i),n(o)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}var uv=bl.exports;class cv extends uv.EventEmitter{constructor(){super();tt(this,"mediaRecorder",null);tt(this,"audioStream",null);tt(this,"audioChunks",[]);tt(this,"isRecording",!1);tt(this,"recordingStartTime",0);tt(this,"recordingTimer",null)}async getDevices(){try{return await navigator.mediaDevices.getUserMedia({audio:!0}),(await navigator.mediaDevices.enumerateDevices()).filter(r=>r.kind==="audioinput")}catch(n){throw console.error("Failed to get audio devices:",n),new Error("無法獲取音頻設備列表")}}async startRecording(n){try{if(this.isRecording)throw new Error("Already recording");const r={audio:{deviceId:n?{exact:n}:void 0,sampleRate:pr.sampleRate,channelCount:pr.channels,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}};this.audioStream=await navigator.mediaDevices.getUserMedia(r);const i=["audio/webm;codecs=opus","audio/webm","audio/mp4","audio/wav"];let o="";for(const s of i)if(MediaRecorder.isTypeSupported(s)){o=s;break}if(!o)throw new Error("No supported audio format found");this.mediaRecorder=new MediaRecorder(this.audioStream,{mimeType:o,audioBitsPerSecond:128e3}),this.setupMediaRecorderEvents(),this.audioChunks=[],this.mediaRecorder.start(100),this.isRecording=!0,this.recordingStartTime=Date.now(),this.recordingTimer=window.setTimeout(()=>{this.isRecording&&this.stopRecording()},pr.maxDuration*1e3),this.emit("recording-started"),console.log("Recording started")}catch(r){if(console.error("Failed to start recording:",r),await this.cleanup(),r instanceof Error){if(r.name==="NotAllowedError")throw new Error("麥克風權限被拒絕");if(r.name==="NotFoundError")throw new Error("找不到麥克風設備");if(r.name==="NotReadableError")throw new Error("麥克風設備被其他應用程式占用")}throw new Error("錄音啟動失敗")}}async stopRecording(){try{if(!this.isRecording||!this.mediaRecorder)return;const n=(Date.now()-this.recordingStartTime)/1e3;if(n<pr.minDuration)throw new Error(`錄音時間太短，至少需要 ${pr.minDuration} 秒`);this.isRecording=!1,this.recordingTimer&&(clearTimeout(this.recordingTimer),this.recordingTimer=null),this.mediaRecorder.stop(),console.log(`Recording stopped after ${n.toFixed(2)} seconds`)}catch(n){console.error("Failed to stop recording:",n),this.emit("recording-error",n),await this.cleanup()}}setupMediaRecorderEvents(){this.mediaRecorder&&(this.mediaRecorder.ondataavailable=n=>{n.data.size>0&&this.audioChunks.push(n.data)},this.mediaRecorder.onstop=async()=>{var n;try{const i=await new Blob(this.audioChunks,{type:((n=this.mediaRecorder)==null?void 0:n.mimeType)||"audio/webm"}).arrayBuffer(),o=new Uint8Array(i);this.emit("recording-stopped",o),await this.cleanup()}catch(r){console.error("Error processing recorded audio:",r),this.emit("recording-error",r),await this.cleanup()}},this.mediaRecorder.onerror=n=>{console.error("MediaRecorder error:",n),this.emit("recording-error",new Error("錄音過程中發生錯誤")),this.cleanup()})}getRecordingState(){const n=this.isRecording?(Date.now()-this.recordingStartTime)/1e3:0;return{isRecording:this.isRecording,duration:n}}async checkMicrophonePermission(){try{return(await navigator.permissions.query({name:"microphone"})).state==="granted"}catch(n){return console.error("Error checking microphone permission:",n),!1}}async requestMicrophonePermission(){try{return(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(r=>r.stop()),!0}catch(n){return console.error("Error requesting microphone permission:",n),!1}}async cleanup(){try{this.isRecording=!1,this.recordingTimer&&(clearTimeout(this.recordingTimer),this.recordingTimer=null),this.mediaRecorder&&this.mediaRecorder.state!=="inactive"&&this.mediaRecorder.stop(),this.mediaRecorder=null,this.audioStream&&(this.audioStream.getTracks().forEach(n=>n.stop()),this.audioStream=null),this.audioChunks=[],this.recordingStartTime=0}catch(n){console.error("Error during cleanup:",n)}}static isAudioSupported(){return!!(navigator.mediaDevices&&typeof navigator.mediaDevices.getUserMedia=="function"&&window.MediaRecorder)}static getSupportedMimeTypes(){return["audio/webm;codecs=opus","audio/webm;codecs=vp8,opus","audio/webm","audio/mp4;codecs=mp4a.40.2","audio/mp4","audio/wav","audio/ogg;codecs=opus"].filter(r=>MediaRecorder.isTypeSupported(r))}}const dv={config:nv,recordingState:{isRecording:!1,mode:"ai",duration:0,status:"idle"},audioDevices:[],isLoading:!1,error:null,notifications:[],showRecordingWindow:!1,showSettingsWindow:!1};function fv(e,t){switch(t.type){case"SET_CONFIG":return{...e,config:t.payload};case"UPDATE_CONFIG":return{...e,config:{...e.config,...t.payload}};case"SET_RECORDING_STATE":return{...e,recordingState:t.payload};case"SET_AUDIO_DEVICES":return{...e,audioDevices:t.payload};case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_ERROR":return{...e,error:t.payload};case"ADD_NOTIFICATION":return{...e,notifications:[...e.notifications,t.payload]};case"REMOVE_NOTIFICATION":return{...e,notifications:e.notifications.filter(n=>n.id!==t.payload)};case"CLEAR_NOTIFICATIONS":return{...e,notifications:[]};case"SHOW_RECORDING_WINDOW":return{...e,showRecordingWindow:t.payload};case"SHOW_SETTINGS_WINDOW":return{...e,showSettingsWindow:t.payload};default:return e}}const Zp=E.createContext(void 0);function pv({children:e}){const[t,n]=E.useReducer(fv,dv),r=jo.useRef(null);E.useEffect(()=>(i(),o(),a(),()=>{s(),l()}),[]);const i=async()=>{try{if(n({type:"SET_LOADING",payload:!0}),!window.electronAPI){console.error("❌ electronAPI not available"),n({type:"SET_ERROR",payload:"API 不可用"}),n({type:"SET_LOADING",payload:!1});return}console.log("✅ electronAPI available, loading config...");const S=await window.electronAPI.getConfig();n({type:"SET_CONFIG",payload:S}),await v()}catch(S){console.error("Failed to initialize app:",S),n({type:"SET_ERROR",payload:"應用程式初始化失敗"})}finally{n({type:"SET_LOADING",payload:!1})}},o=()=>{var T;if(!window.electronAPI)return console.warn("⚠️ electronAPI not available, skipping event listeners setup"),()=>{};const S=window.electronAPI.onConfigUpdated(C=>{n({type:"SET_CONFIG",payload:C})}),m=window.electronAPI.onRecordingStateChanged(C=>{console.log("🔄 AppContext: Recording state changed:",C),n({type:"SET_RECORDING_STATE",payload:C})}),f=window.electronAPI.onHotkeyTriggered(C=>{p({type:"info",message:`${C.mode==="ai"?"AI":"直接"} 模式已觸發`,duration:2e3})}),h=window.electronAPI.onProcessingResult(C=>{C.success?p({type:"success",message:"語音處理完成",duration:3e3}):p({type:"error",message:C.error||"語音處理失敗",duration:5e3})}),w=window.electronAPI.onErrorOccurred(C=>{n({type:"SET_ERROR",payload:C.message}),p({type:"error",message:C.message,duration:5e3})}),k=(T=window.electronAPI)!=null&&T.addEventListener?window.electronAPI.addEventListener("hide-recording-window",()=>{n({type:"SHOW_RECORDING_WINDOW",payload:!1})}):()=>{};window.__unsubscribeFunctions=[S,m,f,h,w,k]},s=()=>{const S=window.__unsubscribeFunctions;S&&S.forEach(m=>m())},a=()=>{r.current=new cv,r.current.on("recording-started",()=>{n({type:"SET_RECORDING_STATE",payload:{...t.recordingState,isRecording:!0,status:"recording"}})}),r.current.on("recording-stopped",async S=>{n({type:"SET_RECORDING_STATE",payload:{...t.recordingState,isRecording:!1,status:"processing"}});try{const m=Array.from(S);console.log("Audio data received, length:",m.length)}catch(m){console.error("Failed to process audio:",m),n({type:"SET_RECORDING_STATE",payload:{...t.recordingState,status:"error",error:"音頻處理失敗"}})}}),r.current.on("recording-error",S=>{n({type:"SET_RECORDING_STATE",payload:{...t.recordingState,isRecording:!1,status:"error",error:S.message}}),n({type:"SHOW_RECORDING_WINDOW",payload:!1}),p({type:"error",message:S.message,duration:5e3})})},l=()=>{r.current&&(r.current.cleanup(),r.current=null)},u=async S=>{try{window.electronAPI&&await window.electronAPI.updateConfig(S),n({type:"UPDATE_CONFIG",payload:S}),p({type:"success",message:"設定已更新",duration:2e3})}catch(m){console.error("Failed to update config:",m),p({type:"error",message:"設定更新失敗",duration:3e3})}},d=async S=>{console.log(`🎙️ AppContext: Starting recording in ${S} mode`);try{console.log("🎙️ AppContext: Updating recording state"),n({type:"SET_RECORDING_STATE",payload:{...t.recordingState,mode:S,status:"recording"}}),console.log("🎙️ AppContext: Showing recording window"),n({type:"SHOW_RECORDING_WINDOW",payload:!0}),r.current?(console.log("🎙️ AppContext: Starting audio recorder"),await r.current.startRecording(t.config.recording.deviceId)):console.log("⚠️ AppContext: No audio recorder available")}catch(m){console.error("❌ AppContext: Failed to start recording:",m),n({type:"SET_RECORDING_STATE",payload:{...t.recordingState,status:"error",error:"錄音啟動失敗"}}),n({type:"SHOW_RECORDING_WINDOW",payload:!1}),p({type:"error",message:"錄音啟動失敗",duration:3e3})}},c=async()=>{try{r.current&&await r.current.stopRecording()}catch(S){console.error("Failed to stop recording:",S),n({type:"SET_RECORDING_STATE",payload:{...t.recordingState,status:"error",error:"錄音停止失敗"}}),p({type:"error",message:"錄音停止失敗",duration:3e3})}},p=S=>{const m=Date.now().toString(),f={...S,id:m,duration:S.duration||3e3};n({type:"ADD_NOTIFICATION",payload:f}),f.duration&&f.duration>0&&setTimeout(()=>{n({type:"REMOVE_NOTIFICATION",payload:m})},f.duration)},g=()=>{n({type:"SET_ERROR",payload:null})},v=async()=>{try{if(r.current){const m=(await r.current.getDevices()).map(f=>({deviceId:f.deviceId,label:f.label||`麥克風 ${f.deviceId.slice(0,8)}`,kind:f.kind}));m.find(f=>f.deviceId==="default")||m.unshift({deviceId:"default",label:"預設麥克風",kind:"audioinput"}),n({type:"SET_AUDIO_DEVICES",payload:m})}}catch(S){console.error("Failed to load audio devices:",S),p({type:"warning",message:"無法載入音頻設備列表",duration:3e3})}},x={state:t,dispatch:n,updateConfig:u,startRecording:d,stopRecording:c,showNotification:p,clearError:g,loadAudioDevices:v};return y.jsx(Zp.Provider,{value:x,children:e})}function oi(){const e=E.useContext(Zp);if(e===void 0)throw new Error("useApp must be used within an AppProvider");return e}const qp=E.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Ko=E.createContext({}),Go=E.createContext(null),Qo=typeof document<"u",$l=Qo?E.useLayoutEffect:E.useEffect,Jp=E.createContext({strict:!1}),Wl=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),hv="framerAppearId",eh="data-"+Wl(hv);function mv(e,t,n,r){const{visualElement:i}=E.useContext(Ko),o=E.useContext(Jp),s=E.useContext(Go),a=E.useContext(qp).reducedMotion,l=E.useRef();r=r||o.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;E.useInsertionEffect(()=>{u&&u.update(n,s)});const d=E.useRef(!!(n[eh]&&!window.HandoffComplete));return $l(()=>{u&&(u.render(),d.current&&u.animationState&&u.animationState.animateChanges())}),E.useEffect(()=>{u&&(u.updateFeatures(),!d.current&&u.animationState&&u.animationState.animateChanges(),d.current&&(d.current=!1,window.HandoffComplete=!0))}),u}function In(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function gv(e,t,n){return E.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):In(n)&&(n.current=r))},[t])}function Zr(e){return typeof e=="string"||Array.isArray(e)}function Yo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Hl=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Kl=["initial",...Hl];function Xo(e){return Yo(e.animate)||Kl.some(t=>Zr(e[t]))}function th(e){return!!(Xo(e)||e.variants)}function yv(e,t){if(Xo(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Zr(n)?n:void 0,animate:Zr(r)?r:void 0}}return e.inherit!==!1?t:{}}function vv(e){const{initial:t,animate:n}=yv(e,E.useContext(Ko));return E.useMemo(()=>({initial:t,animate:n}),[Cc(t),Cc(n)])}function Cc(e){return Array.isArray(e)?e.join(" "):e}const Pc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},qr={};for(const e in Pc)qr[e]={isEnabled:t=>Pc[e].some(n=>!!t[n])};function xv(e){for(const t in e)qr[t]={...qr[t],...e[t]}}const Gl=E.createContext({}),nh=E.createContext({}),wv=Symbol.for("motionComponentSymbol");function Sv({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&xv(e);function o(a,l){let u;const d={...E.useContext(qp),...a,layoutId:kv(a)},{isStatic:c}=d,p=vv(a),g=r(a,c);if(!c&&Qo){p.visualElement=mv(i,g,d,t);const v=E.useContext(nh),x=E.useContext(Jp).strict;p.visualElement&&(u=p.visualElement.loadFeatures(d,x,e,v))}return E.createElement(Ko.Provider,{value:p},u&&p.visualElement?E.createElement(u,{visualElement:p.visualElement,...d}):null,n(i,a,gv(g,p.visualElement,l),g,c,p.visualElement))}const s=E.forwardRef(o);return s[wv]=i,s}function kv({layoutId:e}){const t=E.useContext(Gl).id;return t&&e!==void 0?t+"-"+e:e}function Cv(e){function t(r,i={}){return Sv(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Pv=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ql(e){return typeof e!="string"||e.includes("-")?!1:!!(Pv.indexOf(e)>-1||/[A-Z]/.test(e))}const vo={};function Ev(e){Object.assign(vo,e)}const si=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],wn=new Set(si);function rh(e,{layout:t,layoutId:n}){return wn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!vo[e]||e==="opacity")}const je=e=>!!(e&&e.getVelocity),Tv={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Rv=si.length;function Av(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Rv;s++){const a=si[s];if(e[a]!==void 0){const l=Tv[a]||a;o+=`${l}(${e[a]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const ih=e=>t=>typeof t=="string"&&t.startsWith(e),oh=ih("--"),Da=ih("var(--"),jv=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Lv=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Wt=(e,t,n)=>Math.min(Math.max(n,e),t),Sn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Lr={...Sn,transform:e=>Wt(0,1,e)},Ai={...Sn,default:1},Nr=e=>Math.round(e*1e5)/1e5,Zo=/(-)?([\d]*\.?[\d])+/g,sh=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Nv=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ai(e){return typeof e=="string"}const li=e=>({test:t=>ai(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Et=li("deg"),lt=li("%"),N=li("px"),Mv=li("vh"),Dv=li("vw"),Ec={...lt,parse:e=>lt.parse(e)/100,transform:e=>lt.transform(e*100)},Tc={...Sn,transform:Math.round},ah={borderWidth:N,borderTopWidth:N,borderRightWidth:N,borderBottomWidth:N,borderLeftWidth:N,borderRadius:N,radius:N,borderTopLeftRadius:N,borderTopRightRadius:N,borderBottomRightRadius:N,borderBottomLeftRadius:N,width:N,maxWidth:N,height:N,maxHeight:N,size:N,top:N,right:N,bottom:N,left:N,padding:N,paddingTop:N,paddingRight:N,paddingBottom:N,paddingLeft:N,margin:N,marginTop:N,marginRight:N,marginBottom:N,marginLeft:N,rotate:Et,rotateX:Et,rotateY:Et,rotateZ:Et,scale:Ai,scaleX:Ai,scaleY:Ai,scaleZ:Ai,skew:Et,skewX:Et,skewY:Et,distance:N,translateX:N,translateY:N,translateZ:N,x:N,y:N,z:N,perspective:N,transformPerspective:N,opacity:Lr,originX:Ec,originY:Ec,originZ:N,zIndex:Tc,fillOpacity:Lr,strokeOpacity:Lr,numOctaves:Tc};function Yl(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:a}=e;let l=!1,u=!1,d=!0;for(const c in t){const p=t[c];if(oh(c)){o[c]=p;continue}const g=ah[c],v=Lv(p,g);if(wn.has(c)){if(l=!0,s[c]=v,!d)continue;p!==(g.default||0)&&(d=!1)}else c.startsWith("origin")?(u=!0,a[c]=v):i[c]=v}if(t.transform||(l||r?i.transform=Av(e.transform,n,d,r):i.transform&&(i.transform="none")),u){const{originX:c="50%",originY:p="50%",originZ:g=0}=a;i.transformOrigin=`${c} ${p} ${g}`}}const Xl=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function lh(e,t,n){for(const r in t)!je(t[r])&&!rh(r,n)&&(e[r]=t[r])}function _v({transformTemplate:e},t,n){return E.useMemo(()=>{const r=Xl();return Yl(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function Vv(e,t,n){const r=e.style||{},i={};return lh(i,r,e),Object.assign(i,_v(e,t,n)),e.transformValues?e.transformValues(i):i}function Iv(e,t,n){const r={},i=Vv(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const Ov=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function xo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Ov.has(e)}let uh=e=>!xo(e);function zv(e){e&&(uh=t=>t.startsWith("on")?!xo(t):e(t))}try{zv(require("@emotion/is-prop-valid").default)}catch{}function Fv(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(uh(i)||n===!0&&xo(i)||!t&&!xo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Rc(e,t,n){return typeof e=="string"?e:N.transform(t+n*e)}function Bv(e,t,n){const r=Rc(t,e.x,e.width),i=Rc(n,e.y,e.height);return`${r} ${i}`}const Uv={offset:"stroke-dashoffset",array:"stroke-dasharray"},bv={offset:"strokeDashoffset",array:"strokeDasharray"};function $v(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Uv:bv;e[o.offset]=N.transform(-r);const s=N.transform(t),a=N.transform(n);e[o.array]=`${s} ${a}`}function Zl(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},d,c,p){if(Yl(e,u,d,p),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:v,dimensions:x}=e;g.transform&&(x&&(v.transform=g.transform),delete g.transform),x&&(i!==void 0||o!==void 0||v.transform)&&(v.transformOrigin=Bv(x,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),s!==void 0&&$v(g,s,a,l,!1)}const ch=()=>({...Xl(),attrs:{}}),ql=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Wv(e,t,n,r){const i=E.useMemo(()=>{const o=ch();return Zl(o,t,{enableHardwareAcceleration:!1},ql(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};lh(o,e.style,e),i.style={...o,...i.style}}return i}function Hv(e=!1){return(n,r,i,{latestValues:o},s)=>{const l=(Ql(n)?Wv:Iv)(r,o,s,n),d={...Fv(r,typeof n=="string",e),...l,ref:i},{children:c}=r,p=E.useMemo(()=>je(c)?c.get():c,[c]);return E.createElement(n,{...d,children:p})}}function dh(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const fh=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ph(e,t,n,r){dh(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(fh.has(i)?i:Wl(i),t.attrs[i])}function Jl(e,t){const{style:n}=e,r={};for(const i in n)(je(n[i])||t.style&&je(t.style[i])||rh(i,e))&&(r[i]=n[i]);return r}function hh(e,t){const n=Jl(e,t);for(const r in e)if(je(e[r])||je(t[r])){const i=si.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function eu(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function mh(e){const t=E.useRef(null);return t.current===null&&(t.current=e()),t.current}const wo=e=>Array.isArray(e),Kv=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Gv=e=>wo(e)?e[e.length-1]||0:e;function Hi(e){const t=je(e)?e.get():e;return Kv(t)?t.toValue():t}function Qv({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Yv(r,i,o,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const gh=e=>(t,n)=>{const r=E.useContext(Ko),i=E.useContext(Go),o=()=>Qv(e,t,r,i);return n?o():mh(o)};function Yv(e,t,n,r){const i={},o=r(e,{});for(const p in o)i[p]=Hi(o[p]);let{initial:s,animate:a}=e;const l=Xo(e),u=th(e);t&&u&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let d=n?n.initial===!1:!1;d=d||s===!1;const c=d?a:s;return c&&typeof c!="boolean"&&!Yo(c)&&(Array.isArray(c)?c:[c]).forEach(g=>{const v=eu(e,g);if(!v)return;const{transitionEnd:x,transition:S,...m}=v;for(const f in m){let h=m[f];if(Array.isArray(h)){const w=d?h.length-1:0;h=h[w]}h!==null&&(i[f]=h)}for(const f in x)i[f]=x[f]}),i}const J=e=>e;class Ac{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Xv(e){let t=new Ac,n=new Ac,r=0,i=!1,o=!1;const s=new WeakSet,a={schedule:(l,u=!1,d=!1)=>{const c=d&&i,p=c?t:n;return u&&s.add(l),p.add(l)&&c&&i&&(r=t.order.length),l},cancel:l=>{n.remove(l),s.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const d=t.order[u];d(l),s.has(d)&&(a.schedule(d),e())}i=!1,o&&(o=!1,a.process(l))}};return a}const ji=["prepare","read","update","preRender","render","postRender"],Zv=40;function qv(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=ji.reduce((c,p)=>(c[p]=Xv(()=>n=!0),c),{}),s=c=>o[c].process(i),a=()=>{const c=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(c-i.timestamp,Zv),1),i.timestamp=c,i.isProcessing=!0,ji.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:ji.reduce((c,p)=>{const g=o[p];return c[p]=(v,x=!1,S=!1)=>(n||l(),g.schedule(v,x,S)),c},{}),cancel:c=>ji.forEach(p=>o[p].cancel(c)),state:i,steps:o}}const{schedule:U,cancel:St,state:me,steps:Rs}=qv(typeof requestAnimationFrame<"u"?requestAnimationFrame:J,!0),Jv={useVisualState:gh({scrapeMotionValuesFromProps:hh,createRenderState:ch,onMount:(e,t,{renderState:n,latestValues:r})=>{U.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),U.render(()=>{Zl(n,r,{enableHardwareAcceleration:!1},ql(t.tagName),e.transformTemplate),ph(t,n)})}})},e0={useVisualState:gh({scrapeMotionValuesFromProps:Jl,createRenderState:Xl})};function t0(e,{forwardMotionProps:t=!1},n,r){return{...Ql(e)?Jv:e0,preloadedFeatures:n,useRender:Hv(t),createVisualElement:r,Component:e}}function pt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const yh=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function qo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const n0=e=>t=>yh(t)&&e(t,qo(t));function mt(e,t,n,r){return pt(e,t,n0(n),r)}const r0=(e,t)=>n=>t(e(n)),Ut=(...e)=>e.reduce(r0);function vh(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const jc=vh("dragHorizontal"),Lc=vh("dragVertical");function xh(e){let t=!1;if(e==="y")t=Lc();else if(e==="x")t=jc();else{const n=jc(),r=Lc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function wh(){const e=xh(!0);return e?(e(),!1):!0}class Yt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Nc(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||wh())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&U.update(()=>a[r](o,s))};return mt(e.current,n,i,{passive:!e.getProps()[r]})}class i0 extends Yt{mount(){this.unmount=Ut(Nc(this.node,!0),Nc(this.node,!1))}unmount(){}}class o0 extends Yt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ut(pt(this.node.current,"focus",()=>this.onFocus()),pt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Sh=(e,t)=>t?e===t?!0:Sh(e,t.parentElement):!1;function As(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,qo(n))}class s0 extends Yt{constructor(){super(...arguments),this.removeStartListeners=J,this.removeEndListeners=J,this.removeAccessibleListeners=J,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=mt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:d,globalTapTarget:c}=this.node.getProps();U.update(()=>{!c&&!Sh(this.node.current,a.target)?d&&d(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),s=mt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ut(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||As("up",(l,u)=>{const{onTap:d}=this.node.getProps();d&&U.update(()=>d(l,u))})};this.removeEndListeners(),this.removeEndListeners=pt(this.node.current,"keyup",s),As("down",(a,l)=>{this.startPress(a,l)})},n=pt(this.node.current,"keydown",t),r=()=>{this.isPressing&&As("cancel",(o,s)=>this.cancelPress(o,s))},i=pt(this.node.current,"blur",r);this.removeAccessibleListeners=Ut(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&U.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!wh()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&U.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=mt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=pt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ut(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const _a=new WeakMap,js=new WeakMap,a0=e=>{const t=_a.get(e.target);t&&t(e)},l0=e=>{e.forEach(a0)};function u0({root:e,...t}){const n=e||document;js.has(n)||js.set(n,{});const r=js.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(l0,{root:e,...t})),r[i]}function c0(e,t,n){const r=u0(t);return _a.set(e,n),r.observe(e),()=>{_a.delete(e),r.unobserve(e)}}const d0={some:0,all:1};class f0 extends Yt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:d0[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:d,onViewportLeave:c}=this.node.getProps(),p=u?d:c;p&&p(l)};return c0(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(p0(t,n))&&this.startObserver()}unmount(){}}function p0({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const h0={inView:{Feature:f0},tap:{Feature:s0},focus:{Feature:o0},hover:{Feature:i0}};function kh(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function m0(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function g0(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Jo(e,t,n){const r=e.getProps();return eu(r,t,n!==void 0?n:r.custom,m0(e),g0(e))}let tu=J;const fn=e=>e*1e3,gt=e=>e/1e3,y0={current:!1},Ch=e=>Array.isArray(e)&&typeof e[0]=="number";function Ph(e){return!!(!e||typeof e=="string"&&Eh[e]||Ch(e)||Array.isArray(e)&&e.every(Ph))}const wr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Eh={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:wr([0,.65,.55,1]),circOut:wr([.55,0,1,.45]),backIn:wr([.31,.01,.66,-.59]),backOut:wr([.33,1.53,.69,.99])};function Th(e){if(e)return Ch(e)?wr(e):Array.isArray(e)?e.map(Th):Eh[e]}function v0(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const d=Th(a);return Array.isArray(d)&&(u.easing=d),e.animate(u,{delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function x0(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Rh=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,w0=1e-7,S0=12;function k0(e,t,n,r,i){let o,s,a=0;do s=t+(n-t)/2,o=Rh(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>w0&&++a<S0);return s}function ui(e,t,n,r){if(e===t&&n===r)return J;const i=o=>k0(o,0,1,e,n);return o=>o===0||o===1?o:Rh(i(o),t,r)}const C0=ui(.42,0,1,1),P0=ui(0,0,.58,1),Ah=ui(.42,0,.58,1),E0=e=>Array.isArray(e)&&typeof e[0]!="number",jh=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Lh=e=>t=>1-e(1-t),nu=e=>1-Math.sin(Math.acos(e)),Nh=Lh(nu),T0=jh(nu),Mh=ui(.33,1.53,.69,.99),ru=Lh(Mh),R0=jh(ru),A0=e=>(e*=2)<1?.5*ru(e):.5*(2-Math.pow(2,-10*(e-1))),j0={linear:J,easeIn:C0,easeInOut:Ah,easeOut:P0,circIn:nu,circInOut:T0,circOut:Nh,backIn:ru,backInOut:R0,backOut:Mh,anticipate:A0},Mc=e=>{if(Array.isArray(e)){tu(e.length===4);const[t,n,r,i]=e;return ui(t,n,r,i)}else if(typeof e=="string")return j0[e];return e},iu=(e,t)=>n=>!!(ai(n)&&Nv.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Dh=(e,t,n)=>r=>{if(!ai(r))return r;const[i,o,s,a]=r.match(Zo);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},L0=e=>Wt(0,255,e),Ls={...Sn,transform:e=>Math.round(L0(e))},un={test:iu("rgb","red"),parse:Dh("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Ls.transform(e)+", "+Ls.transform(t)+", "+Ls.transform(n)+", "+Nr(Lr.transform(r))+")"};function N0(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Va={test:iu("#"),parse:N0,transform:un.transform},On={test:iu("hsl","hue"),parse:Dh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+lt.transform(Nr(t))+", "+lt.transform(Nr(n))+", "+Nr(Lr.transform(r))+")"},xe={test:e=>un.test(e)||Va.test(e)||On.test(e),parse:e=>un.test(e)?un.parse(e):On.test(e)?On.parse(e):Va.parse(e),transform:e=>ai(e)?e:e.hasOwnProperty("red")?un.transform(e):On.transform(e)},G=(e,t,n)=>-n*e+n*t+e;function Ns(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function M0({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=Ns(l,a,e+1/3),o=Ns(l,a,e),s=Ns(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const Ms=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},D0=[Va,un,On],_0=e=>D0.find(t=>t.test(e));function Dc(e){const t=_0(e);let n=t.parse(e);return t===On&&(n=M0(n)),n}const _h=(e,t)=>{const n=Dc(e),r=Dc(t),i={...n};return o=>(i.red=Ms(n.red,r.red,o),i.green=Ms(n.green,r.green,o),i.blue=Ms(n.blue,r.blue,o),i.alpha=G(n.alpha,r.alpha,o),un.transform(i))};function V0(e){var t,n;return isNaN(e)&&ai(e)&&(((t=e.match(Zo))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(sh))===null||n===void 0?void 0:n.length)||0)>0}const Vh={regex:jv,countKey:"Vars",token:"${v}",parse:J},Ih={regex:sh,countKey:"Colors",token:"${c}",parse:xe.parse},Oh={regex:Zo,countKey:"Numbers",token:"${n}",parse:Sn.parse};function Ds(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function So(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Ds(n,Vh),Ds(n,Ih),Ds(n,Oh),n}function zh(e){return So(e).values}function Fh(e){const{values:t,numColors:n,numVars:r,tokenised:i}=So(e),o=t.length;return s=>{let a=i;for(let l=0;l<o;l++)l<r?a=a.replace(Vh.token,s[l]):l<r+n?a=a.replace(Ih.token,xe.transform(s[l])):a=a.replace(Oh.token,Nr(s[l]));return a}}const I0=e=>typeof e=="number"?0:e;function O0(e){const t=zh(e);return Fh(e)(t.map(I0))}const Ht={test:V0,parse:zh,createTransformer:Fh,getAnimatableNone:O0},Bh=(e,t)=>n=>`${n>0?t:e}`;function Uh(e,t){return typeof e=="number"?n=>G(e,t,n):xe.test(e)?_h(e,t):e.startsWith("var(")?Bh(e,t):$h(e,t)}const bh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>Uh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},z0=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Uh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},$h=(e,t)=>{const n=Ht.createTransformer(t),r=So(e),i=So(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ut(bh(r.values,i.values),n):Bh(e,t)},Jr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},_c=(e,t)=>n=>G(e,t,n);function F0(e){return typeof e=="number"?_c:typeof e=="string"?xe.test(e)?_h:$h:Array.isArray(e)?bh:typeof e=="object"?z0:_c}function B0(e,t,n){const r=[],i=n||F0(e[0]),o=e.length-1;for(let s=0;s<o;s++){let a=i(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||J:t;a=Ut(l,a)}r.push(a)}return r}function Wh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(tu(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=B0(t,r,i),a=s.length,l=u=>{let d=0;if(a>1)for(;d<e.length-2&&!(u<e[d+1]);d++);const c=Jr(e[d],e[d+1],u);return s[d](c)};return n?u=>l(Wt(e[0],e[o-1],u)):l}function U0(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Jr(0,t,r);e.push(G(n,1,i))}}function b0(e){const t=[0];return U0(t,e.length-1),t}function $0(e,t){return e.map(n=>n*t)}function W0(e,t){return e.map(()=>t||Ah).splice(0,e.length-1)}function ko({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=E0(r)?r.map(Mc):Mc(r),o={done:!1,value:t[0]},s=$0(n&&n.length===t.length?n:b0(t),e),a=Wh(s,t,{ease:Array.isArray(i)?i:W0(t,i)});return{calculatedDuration:e,next:l=>(o.value=a(l),o.done=l>=e,o)}}function Hh(e,t){return t?e*(1e3/t):0}const H0=5;function Kh(e,t,n){const r=Math.max(t-H0,0);return Hh(n-e(r),t-r)}const _s=.001,K0=.01,G0=10,Q0=.05,Y0=1;function X0({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=Wt(Q0,Y0,s),e=Wt(K0,G0,gt(e)),s<1?(i=u=>{const d=u*s,c=d*e,p=d-n,g=Ia(u,s),v=Math.exp(-c);return _s-p/g*v},o=u=>{const c=u*s*e,p=c*n+n,g=Math.pow(s,2)*Math.pow(u,2)*e,v=Math.exp(-c),x=Ia(Math.pow(u,2),s);return(-i(u)+_s>0?-1:1)*((p-g)*v)/x}):(i=u=>{const d=Math.exp(-u*e),c=(u-n)*e+1;return-_s+d*c},o=u=>{const d=Math.exp(-u*e),c=(n-u)*(e*e);return d*c});const a=5/e,l=q0(i,o,a);if(e=fn(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const Z0=12;function q0(e,t,n){let r=n;for(let i=1;i<Z0;i++)r=r-e(r)/t(r);return r}function Ia(e,t){return e*Math.sqrt(1-t*t)}const J0=["duration","bounce"],e1=["stiffness","damping","mass"];function Vc(e,t){return t.some(n=>e[n]!==void 0)}function t1(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Vc(e,e1)&&Vc(e,J0)){const n=X0(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Gh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:d,velocity:c,isResolvedFromDuration:p}=t1({...r,velocity:-gt(r.velocity||0)}),g=c||0,v=l/(2*Math.sqrt(a*u)),x=o-i,S=gt(Math.sqrt(a/u)),m=Math.abs(x)<5;n||(n=m?.01:2),t||(t=m?.005:.5);let f;if(v<1){const h=Ia(S,v);f=w=>{const k=Math.exp(-v*S*w);return o-k*((g+v*S*x)/h*Math.sin(h*w)+x*Math.cos(h*w))}}else if(v===1)f=h=>o-Math.exp(-S*h)*(x+(g+S*x)*h);else{const h=S*Math.sqrt(v*v-1);f=w=>{const k=Math.exp(-v*S*w),T=Math.min(h*w,300);return o-k*((g+v*S*x)*Math.sinh(T)+h*x*Math.cosh(T))/h}}return{calculatedDuration:p&&d||null,next:h=>{const w=f(h);if(p)s.done=h>=d;else{let k=g;h!==0&&(v<1?k=Kh(f,h,w):k=0);const T=Math.abs(k)<=n,C=Math.abs(o-w)<=t;s.done=T&&C}return s.value=s.done?o:w,s}}}function Ic({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:d}){const c=e[0],p={done:!1,value:c},g=R=>a!==void 0&&R<a||l!==void 0&&R>l,v=R=>a===void 0?l:l===void 0||Math.abs(a-R)<Math.abs(l-R)?a:l;let x=n*t;const S=c+x,m=s===void 0?S:s(S);m!==S&&(x=m-c);const f=R=>-x*Math.exp(-R/r),h=R=>m+f(R),w=R=>{const L=f(R),M=h(R);p.done=Math.abs(L)<=u,p.value=p.done?m:M};let k,T;const C=R=>{g(p.value)&&(k=R,T=Gh({keyframes:[p.value,v(p.value)],velocity:Kh(h,R,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:d}))};return C(0),{calculatedDuration:null,next:R=>{let L=!1;return!T&&k===void 0&&(L=!0,w(R),C(R)),k!==void 0&&R>k?T.next(R-k):(!L&&w(R),p)}}}const n1=e=>{const t=({timestamp:n})=>e(n);return{start:()=>U.update(t,!0),stop:()=>St(t),now:()=>me.isProcessing?me.timestamp:performance.now()}},Oc=2e4;function zc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Oc;)t+=n,r=e.next(t);return t>=Oc?1/0:t}const r1={decay:Ic,inertia:Ic,tween:ko,keyframes:ko,spring:Gh};function Co({autoplay:e=!0,delay:t=0,driver:n=n1,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:d,onUpdate:c,...p}){let g=1,v=!1,x,S;const m=()=>{S=new Promise(_=>{x=_})};m();let f;const h=r1[i]||ko;let w;h!==ko&&typeof r[0]!="number"&&(w=Wh([0,100],r,{clamp:!1}),r=[0,100]);const k=h({...p,keyframes:r});let T;a==="mirror"&&(T=h({...p,keyframes:[...r].reverse(),velocity:-(p.velocity||0)}));let C="idle",R=null,L=null,M=null;k.calculatedDuration===null&&o&&(k.calculatedDuration=zc(k));const{calculatedDuration:oe}=k;let ue=1/0,ve=1/0;oe!==null&&(ue=oe+s,ve=ue*(o+1)-s);let se=0;const Ct=_=>{if(L===null)return;g>0&&(L=Math.min(L,_)),g<0&&(L=Math.min(_-ve/g,L)),R!==null?se=R:se=Math.round(_-L)*g;const W=se-t*(g>=0?1:-1),Xt=g>=0?W<0:W>ve;se=Math.max(W,0),C==="finished"&&R===null&&(se=ve);let et=se,kn=k;if(o){const es=Math.min(se,ve)/ue;let ci=Math.floor(es),qt=es%1;!qt&&es>=1&&(qt=1),qt===1&&ci--,ci=Math.min(ci,o+1),!!(ci%2)&&(a==="reverse"?(qt=1-qt,s&&(qt-=s/ue)):a==="mirror"&&(kn=T)),et=Wt(0,1,qt)*ue}const Le=Xt?{done:!1,value:r[0]}:kn.next(et);w&&(Le.value=w(Le.value));let{done:Zt}=Le;!Xt&&oe!==null&&(Zt=g>=0?se>=ve:se<=0);const Cm=R===null&&(C==="finished"||C==="running"&&Zt);return c&&c(Le.value),Cm&&A(),Le},Z=()=>{f&&f.stop(),f=void 0},ze=()=>{C="idle",Z(),x(),m(),L=M=null},A=()=>{C="finished",d&&d(),Z(),x()},D=()=>{if(v)return;f||(f=n(Ct));const _=f.now();l&&l(),R!==null?L=_-R:(!L||C==="finished")&&(L=_),C==="finished"&&m(),M=L,R=null,C="running",f.start()};e&&D();const V={then(_,W){return S.then(_,W)},get time(){return gt(se)},set time(_){_=fn(_),se=_,R!==null||!f||g===0?R=_:L=f.now()-_/g},get duration(){const _=k.calculatedDuration===null?zc(k):k.calculatedDuration;return gt(_)},get speed(){return g},set speed(_){_===g||!f||(g=_,V.time=gt(se))},get state(){return C},play:D,pause:()=>{C="paused",R=se},stop:()=>{v=!0,C!=="idle"&&(C="idle",u&&u(),ze())},cancel:()=>{M!==null&&Ct(M),ze()},complete:()=>{C="finished"},sample:_=>(L=0,Ct(_))};return V}function i1(e){let t;return()=>(t===void 0&&(t=e()),t)}const o1=i1(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),s1=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Li=10,a1=2e4,l1=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Ph(t.ease);function u1(e,t,{onUpdate:n,onComplete:r,...i}){if(!(o1()&&s1.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,a,l,u=!1;const d=()=>{l=new Promise(h=>{a=h})};d();let{keyframes:c,duration:p=300,ease:g,times:v}=i;if(l1(t,i)){const h=Co({...i,repeat:0,delay:0});let w={done:!1,value:c[0]};const k=[];let T=0;for(;!w.done&&T<a1;)w=h.sample(T),k.push(w.value),T+=Li;v=void 0,c=k,p=T-Li,g="linear"}const x=v0(e.owner.current,t,c,{...i,duration:p,ease:g,times:v}),S=()=>{u=!1,x.cancel()},m=()=>{u=!0,U.update(S),a(),d()};return x.onfinish=()=>{u||(e.set(x0(c,i)),r&&r(),m())},{then(h,w){return l.then(h,w)},attachTimeline(h){return x.timeline=h,x.onfinish=null,J},get time(){return gt(x.currentTime||0)},set time(h){x.currentTime=fn(h)},get speed(){return x.playbackRate},set speed(h){x.playbackRate=h},get duration(){return gt(p)},play:()=>{s||(x.play(),St(S))},pause:()=>x.pause(),stop:()=>{if(s=!0,x.playState==="idle")return;const{currentTime:h}=x;if(h){const w=Co({...i,autoplay:!1});e.setWithVelocity(w.sample(h-Li).value,w.sample(h).value,Li)}m()},complete:()=>{u||x.finish()},cancel:m}}function c1({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:J,pause:J,stop:J,then:o=>(o(),Promise.resolve()),cancel:J,complete:J});return t?Co({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const d1={type:"spring",stiffness:500,damping:25,restSpeed:10},f1=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),p1={type:"keyframes",duration:.8},h1={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},m1=(e,{keyframes:t})=>t.length>2?p1:wn.has(e)?e.startsWith("scale")?f1(t[1]):d1:h1,Oa=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Ht.test(t)||t==="0")&&!t.startsWith("url(")),g1=new Set(["brightness","contrast","saturate","opacity"]);function y1(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Zo)||[];if(!r)return e;const i=n.replace(r,"");let o=g1.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const v1=/([a-z-]*)\(.*?\)/g,za={...Ht,getAnimatableNone:e=>{const t=e.match(v1);return t?t.map(y1).join(" "):e}},x1={...ah,color:xe,backgroundColor:xe,outlineColor:xe,fill:xe,stroke:xe,borderColor:xe,borderTopColor:xe,borderRightColor:xe,borderBottomColor:xe,borderLeftColor:xe,filter:za,WebkitFilter:za},ou=e=>x1[e];function Qh(e,t){let n=ou(e);return n!==za&&(n=Ht),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Yh=e=>/^0[^.\s]+$/.test(e);function w1(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Yh(e)}function S1(e,t,n,r){const i=Oa(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),w1(o[u])&&l.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(a=o[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++){const d=l[u];o[d]=Qh(t,a)}return o}function k1({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...d}){return!!Object.keys(d).length}function su(e,t){return e[t]||e.default||e}const C1={skipAnimations:!1},au=(e,t,n,r={})=>i=>{const o=su(r,e)||{},s=o.delay||r.delay||0;let{elapsed:a=0}=r;a=a-fn(s);const l=S1(t,e,n,o),u=l[0],d=l[l.length-1],c=Oa(e,u),p=Oa(e,d);let g={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:v=>{t.set(v),o.onUpdate&&o.onUpdate(v)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(k1(o)||(g={...g,...m1(e,g)}),g.duration&&(g.duration=fn(g.duration)),g.repeatDelay&&(g.repeatDelay=fn(g.repeatDelay)),!c||!p||y0.current||o.type===!1||C1.skipAnimations)return c1(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const v=u1(t,e,g);if(v)return v}return Co(g)};function Po(e){return!!(je(e)&&e.add)}const Xh=e=>/^\-?\d*\.?\d+$/.test(e);function lu(e,t){e.indexOf(t)===-1&&e.push(t)}function uu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class cu{constructor(){this.subscriptions=[]}add(t){return lu(this.subscriptions,t),()=>uu(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const P1=e=>!isNaN(parseFloat(e));class E1{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=me;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,U.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>U.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=P1(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new cu);const r=this.events[t].add(n);return t==="change"?()=>{r(),U.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Hh(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function er(e,t){return new E1(e,t)}const Zh=e=>t=>t.test(e),T1={test:e=>e==="auto",parse:e=>e},qh=[Sn,N,lt,Et,Dv,Mv,T1],hr=e=>qh.find(Zh(e)),R1=[...qh,xe,Ht],A1=e=>R1.find(Zh(e));function j1(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,er(n))}function L1(e,t){const n=Jo(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const a=Gv(o[s]);j1(e,s,a)}}function N1(e,t,n){var r,i;const o=Object.keys(t).filter(a=>!e.hasValue(a)),s=o.length;if(s)for(let a=0;a<s;a++){const l=o[a],u=t[l];let d=null;Array.isArray(u)&&(d=u[0]),d===null&&(d=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),d!=null&&(typeof d=="string"&&(Xh(d)||Yh(d))?d=parseFloat(d):!A1(d)&&Ht.test(u)&&(d=Qh(l,u)),e.addValue(l,er(d,{owner:e})),n[l]===void 0&&(n[l]=d),d!==null&&e.setBaseTarget(l,d))}}function M1(e,t){return t?(t[e]||t.default||t).from:void 0}function D1(e,t,n){const r={};for(const i in e){const o=M1(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function _1({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function V1(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Jh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(o=r);const u=[],d=i&&e.animationState&&e.animationState.getState()[i];for(const c in a){const p=e.getValue(c),g=a[c];if(!p||g===void 0||d&&_1(d,c))continue;const v={delay:n,elapsed:0,...su(o||{},c)};if(window.HandoffAppearAnimations){const m=e.getProps()[eh];if(m){const f=window.HandoffAppearAnimations(m,c,p,U);f!==null&&(v.elapsed=f,v.isHandoff=!0)}}let x=!v.isHandoff&&!V1(p,g);if(v.type==="spring"&&(p.getVelocity()||v.velocity)&&(x=!1),p.animation&&(x=!1),x)continue;p.start(au(c,p,g,e.shouldReduceMotion&&wn.has(c)?{type:!1}:v));const S=p.animation;Po(l)&&(l.add(c),S.then(()=>l.remove(c))),u.push(S)}return s&&Promise.all(u).then(()=>{s&&L1(e,s)}),u}function Fa(e,t,n={}){const r=Jo(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Jh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:d,staggerDirection:c}=i;return I1(e,t,u+l,d,c,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[o,s]:[s,o];return l().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function I1(e,t,n=0,r=0,i=1,o){const s=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(O1).forEach((u,d)=>{u.notify("AnimationStart",t),s.push(Fa(u,t,{...o,delay:n+l(d)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function O1(e,t){return e.sortNodePosition(t)}function z1(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Fa(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Fa(e,t,n);else{const i=typeof t=="function"?Jo(e,t,n.custom):t;r=Promise.all(Jh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const F1=[...Hl].reverse(),B1=Hl.length;function U1(e){return t=>Promise.all(t.map(({animation:n,options:r})=>z1(e,n,r)))}function b1(e){let t=U1(e);const n=W1();let r=!0;const i=(l,u)=>{const d=Jo(e,u);if(d){const{transition:c,transitionEnd:p,...g}=d;l={...l,...g,...p}}return l};function o(l){t=l(e)}function s(l,u){const d=e.getProps(),c=e.getVariantContext(!0)||{},p=[],g=new Set;let v={},x=1/0;for(let m=0;m<B1;m++){const f=F1[m],h=n[f],w=d[f]!==void 0?d[f]:c[f],k=Zr(w),T=f===u?h.isActive:null;T===!1&&(x=m);let C=w===c[f]&&w!==d[f]&&k;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),h.protectedKeys={...v},!h.isActive&&T===null||!w&&!h.prevProp||Yo(w)||typeof w=="boolean")continue;let L=$1(h.prevProp,w)||f===u&&h.isActive&&!C&&k||m>x&&k,M=!1;const oe=Array.isArray(w)?w:[w];let ue=oe.reduce(i,{});T===!1&&(ue={});const{prevResolvedValues:ve={}}=h,se={...ve,...ue},Ct=Z=>{L=!0,g.has(Z)&&(M=!0,g.delete(Z)),h.needsAnimating[Z]=!0};for(const Z in se){const ze=ue[Z],A=ve[Z];if(v.hasOwnProperty(Z))continue;let D=!1;wo(ze)&&wo(A)?D=!kh(ze,A):D=ze!==A,D?ze!==void 0?Ct(Z):g.add(Z):ze!==void 0&&g.has(Z)?Ct(Z):h.protectedKeys[Z]=!0}h.prevProp=w,h.prevResolvedValues=ue,h.isActive&&(v={...v,...ue}),r&&e.blockInitialAnimation&&(L=!1),L&&(!C||M)&&p.push(...oe.map(Z=>({animation:Z,options:{type:f,...l}})))}if(g.size){const m={};g.forEach(f=>{const h=e.getBaseTarget(f);h!==void 0&&(m[f]=h)}),p.push({animation:m})}let S=!!p.length;return r&&(d.initial===!1||d.initial===d.animate)&&!e.manuallyAnimateOnMount&&(S=!1),r=!1,S?t(p):Promise.resolve()}function a(l,u,d){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(g=>{var v;return(v=g.animationState)===null||v===void 0?void 0:v.setActive(l,u)}),n[l].isActive=u;const p=s(d,l);for(const g in n)n[g].protectedKeys={};return p}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n}}function $1(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!kh(t,e):!1}function Jt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function W1(){return{animate:Jt(!0),whileInView:Jt(),whileHover:Jt(),whileTap:Jt(),whileDrag:Jt(),whileFocus:Jt(),exit:Jt()}}class H1 extends Yt{constructor(t){super(t),t.animationState||(t.animationState=b1(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Yo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let K1=0;class G1 extends Yt{constructor(){super(...arguments),this.id=K1++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Q1={animation:{Feature:H1},exit:{Feature:G1}},Fc=(e,t)=>Math.abs(e-t);function Y1(e,t){const n=Fc(e.x,t.x),r=Fc(e.y,t.y);return Math.sqrt(n**2+r**2)}class em{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=Is(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,g=Y1(c.offset,{x:0,y:0})>=3;if(!p&&!g)return;const{point:v}=c,{timestamp:x}=me;this.history.push({...v,timestamp:x});const{onStart:S,onMove:m}=this.handlers;p||(S&&S(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,c)},this.handlePointerMove=(c,p)=>{this.lastMoveEvent=c,this.lastMoveEventInfo=Vs(p,this.transformPagePoint),U.update(this.updatePoint,!0)},this.handlePointerUp=(c,p)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Is(c.type==="pointercancel"?this.lastMoveEventInfo:Vs(p,this.transformPagePoint),this.history);this.startEvent&&g&&g(c,S),v&&v(c,S)},!yh(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=qo(t),a=Vs(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=me;this.history=[{...l,timestamp:u}];const{onSessionStart:d}=n;d&&d(t,Is(a,this.history)),this.removeListeners=Ut(mt(this.contextWindow,"pointermove",this.handlePointerMove),mt(this.contextWindow,"pointerup",this.handlePointerUp),mt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),St(this.updatePoint)}}function Vs(e,t){return t?{point:t(e.point)}:e}function Bc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Is({point:e},t){return{point:e,delta:Bc(e,tm(t)),offset:Bc(e,X1(t)),velocity:Z1(t,.1)}}function X1(e){return e[0]}function tm(e){return e[e.length-1]}function Z1(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=tm(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>fn(t)));)n--;if(!r)return{x:0,y:0};const o=gt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Ve(e){return e.max-e.min}function Ba(e,t=0,n=.01){return Math.abs(e-t)<=n}function Uc(e,t,n,r=.5){e.origin=r,e.originPoint=G(t.min,t.max,e.origin),e.scale=Ve(n)/Ve(t),(Ba(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=G(n.min,n.max,e.origin)-e.originPoint,(Ba(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Mr(e,t,n,r){Uc(e.x,t.x,n.x,r?r.originX:void 0),Uc(e.y,t.y,n.y,r?r.originY:void 0)}function bc(e,t,n){e.min=n.min+t.min,e.max=e.min+Ve(t)}function q1(e,t,n){bc(e.x,t.x,n.x),bc(e.y,t.y,n.y)}function $c(e,t,n){e.min=t.min-n.min,e.max=e.min+Ve(t)}function Dr(e,t,n){$c(e.x,t.x,n.x),$c(e.y,t.y,n.y)}function J1(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?G(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?G(n,e,r.max):Math.min(e,n)),e}function Wc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function ex(e,{top:t,left:n,bottom:r,right:i}){return{x:Wc(e.x,n,i),y:Wc(e.y,t,r)}}function Hc(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function tx(e,t){return{x:Hc(e.x,t.x),y:Hc(e.y,t.y)}}function nx(e,t){let n=.5;const r=Ve(e),i=Ve(t);return i>r?n=Jr(t.min,t.max-r,e.min):r>i&&(n=Jr(e.min,e.max-i,t.min)),Wt(0,1,n)}function rx(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Ua=.35;function ix(e=Ua){return e===!1?e=0:e===!0&&(e=Ua),{x:Kc(e,"left","right"),y:Kc(e,"top","bottom")}}function Kc(e,t,n){return{min:Gc(e,t),max:Gc(e,n)}}function Gc(e,t){return typeof e=="number"?e:e[t]||0}const Qc=()=>({translate:0,scale:1,origin:0,originPoint:0}),zn=()=>({x:Qc(),y:Qc()}),Yc=()=>({min:0,max:0}),ee=()=>({x:Yc(),y:Yc()});function Be(e){return[e("x"),e("y")]}function nm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function ox({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function sx(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Os(e){return e===void 0||e===1}function ba({scale:e,scaleX:t,scaleY:n}){return!Os(e)||!Os(t)||!Os(n)}function nn(e){return ba(e)||rm(e)||e.z||e.rotate||e.rotateX||e.rotateY}function rm(e){return Xc(e.x)||Xc(e.y)}function Xc(e){return e&&e!=="0%"}function Eo(e,t,n){const r=e-n,i=t*r;return n+i}function Zc(e,t,n,r,i){return i!==void 0&&(e=Eo(e,i,r)),Eo(e,n,r)+t}function $a(e,t=0,n=1,r,i){e.min=Zc(e.min,t,n,r,i),e.max=Zc(e.max,t,n,r,i)}function im(e,{x:t,y:n}){$a(e.x,t.translate,t.scale,t.originPoint),$a(e.y,n.translate,n.scale,n.originPoint)}function ax(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let a=0;a<i;a++){o=n[a],s=o.projectionDelta;const l=o.instance;l&&l.style&&l.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Fn(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,im(e,s)),r&&nn(o.latestValues)&&Fn(e,o.latestValues))}t.x=qc(t.x),t.y=qc(t.y)}function qc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function At(e,t){e.min=e.min+t,e.max=e.max+t}function Jc(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=G(e.min,e.max,o);$a(e,t[n],t[r],s,t.scale)}const lx=["x","scaleX","originX"],ux=["y","scaleY","originY"];function Fn(e,t){Jc(e.x,t,lx),Jc(e.y,t,ux)}function om(e,t){return nm(sx(e.getBoundingClientRect(),t))}function cx(e,t,n){const r=om(e,n),{scroll:i}=t;return i&&(At(r.x,i.offset.x),At(r.y,i.offset.y)),r}const sm=({current:e})=>e?e.ownerDocument.defaultView:null,dx=new WeakMap;class fx{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ee(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=d=>{const{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(qo(d,"page").point)},o=(d,c)=>{const{drag:p,dragPropagation:g,onDragStart:v}=this.getProps();if(p&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=xh(p),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Be(S=>{let m=this.getAxisMotionValue(S).get()||0;if(lt.test(m)){const{projection:f}=this.visualElement;if(f&&f.layout){const h=f.layout.layoutBox[S];h&&(m=Ve(h)*(parseFloat(m)/100))}}this.originPoint[S]=m}),v&&U.update(()=>v(d,c),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(d,c)=>{const{dragPropagation:p,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!p&&!this.openGlobalLock)return;const{offset:S}=c;if(g&&this.currentDirection===null){this.currentDirection=px(S),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",c.point,S),this.updateAxis("y",c.point,S),this.visualElement.render(),x&&x(d,c)},a=(d,c)=>this.stop(d,c),l=()=>Be(d=>{var c;return this.getAnimationState(d)==="paused"&&((c=this.getAxisMotionValue(d).animation)===null||c===void 0?void 0:c.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new em(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:sm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&U.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Ni(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=J1(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&In(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=ex(i.layoutBox,n):this.constraints=!1,this.elastic=ix(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Be(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=rx(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!In(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=cx(r,i.root,this.visualElement.getTransformPagePoint());let s=tx(i.layout.layoutBox,o);if(n){const a=n(ox(s));this.hasMutatedConstraints=!!a,a&&(s=nm(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Be(d=>{if(!Ni(d,n,this.currentDirection))return;let c=l&&l[d]||{};s&&(c={min:0,max:0});const p=i?200:1e6,g=i?40:1e7,v={type:"inertia",velocity:r?t[d]:0,bounceStiffness:p,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...c};return this.startAxisValueAnimation(d,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(au(t,r,0,n))}stopAnimation(){Be(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Be(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Be(n=>{const{drag:r}=this.getProps();if(!Ni(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:a}=i.layout.layoutBox[n];o.set(t[n]-G(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!In(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Be(s=>{const a=this.getAxisMotionValue(s);if(a){const l=a.get();i[s]=nx({min:l,max:l},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Be(s=>{if(!Ni(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(G(l,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;dx.set(this.visualElement,this);const t=this.visualElement.current,n=mt(t,"pointerdown",l=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();In(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=pt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Be(d=>{const c=this.getAxisMotionValue(d);c&&(this.originPoint[d]+=l[d].translate,c.set(c.get()+l[d].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Ua,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function Ni(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function px(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class hx extends Yt{constructor(t){super(t),this.removeGroupControls=J,this.removeListeners=J,this.controls=new fx(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||J}unmount(){this.removeGroupControls(),this.removeListeners()}}const ed=e=>(t,n)=>{e&&U.update(()=>e(t,n))};class mx extends Yt{constructor(){super(...arguments),this.removePointerDownListener=J}onPointerDown(t){this.session=new em(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:sm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:ed(t),onStart:ed(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&U.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=mt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function gx(){const e=E.useContext(Go);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=E.useId();return E.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Ki={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function td(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const mr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(N.test(e))e=parseFloat(e);else return e;const n=td(e,t.target.x),r=td(e,t.target.y);return`${n}% ${r}%`}},yx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Ht.parse(e);if(i.length>5)return r;const o=Ht.createTransformer(e),s=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+s]/=a,i[1+s]/=l;const u=G(a,l,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class vx extends jo.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Ev(xx),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ki.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||U.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function am(e){const[t,n]=gx(),r=E.useContext(Gl);return jo.createElement(vx,{...e,layoutGroup:r,switchLayoutGroup:E.useContext(nh),isPresent:t,safeToRemove:n})}const xx={borderRadius:{...mr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:mr,borderTopRightRadius:mr,borderBottomLeftRadius:mr,borderBottomRightRadius:mr,boxShadow:yx},lm=["TopLeft","TopRight","BottomLeft","BottomRight"],wx=lm.length,nd=e=>typeof e=="string"?parseFloat(e):e,rd=e=>typeof e=="number"||N.test(e);function Sx(e,t,n,r,i,o){i?(e.opacity=G(0,n.opacity!==void 0?n.opacity:1,kx(r)),e.opacityExit=G(t.opacity!==void 0?t.opacity:1,0,Cx(r))):o&&(e.opacity=G(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<wx;s++){const a=`border${lm[s]}Radius`;let l=id(t,a),u=id(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||rd(l)===rd(u)?(e[a]=Math.max(G(nd(l),nd(u),r),0),(lt.test(u)||lt.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=G(t.rotate||0,n.rotate||0,r))}function id(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const kx=um(0,.5,Nh),Cx=um(.5,.95,J);function um(e,t,n){return r=>r<e?0:r>t?1:n(Jr(e,t,r))}function od(e,t){e.min=t.min,e.max=t.max}function Fe(e,t){od(e.x,t.x),od(e.y,t.y)}function sd(e,t,n,r,i){return e-=t,e=Eo(e,1/n,r),i!==void 0&&(e=Eo(e,1/i,r)),e}function Px(e,t=0,n=1,r=.5,i,o=e,s=e){if(lt.test(t)&&(t=parseFloat(t),t=G(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=G(o.min,o.max,r);e===o&&(a-=t),e.min=sd(e.min,t,n,a,i),e.max=sd(e.max,t,n,a,i)}function ad(e,t,[n,r,i],o,s){Px(e,t[n],t[r],t[i],t.scale,o,s)}const Ex=["x","scaleX","originX"],Tx=["y","scaleY","originY"];function ld(e,t,n,r){ad(e.x,t,Ex,n?n.x:void 0,r?r.x:void 0),ad(e.y,t,Tx,n?n.y:void 0,r?r.y:void 0)}function ud(e){return e.translate===0&&e.scale===1}function cm(e){return ud(e.x)&&ud(e.y)}function Rx(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function dm(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function cd(e){return Ve(e.x)/Ve(e.y)}class Ax{constructor(){this.members=[]}add(t){lu(this.members,t),t.scheduleRender()}remove(t){if(uu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function dd(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:d}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),d&&(r+=`rotateY(${d}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const jx=(e,t)=>e.depth-t.depth;class Lx{constructor(){this.children=[],this.isDirty=!1}add(t){lu(this.children,t),this.isDirty=!0}remove(t){uu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(jx),this.isDirty=!1,this.children.forEach(t)}}function Nx(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(St(r),e(o-t))};return U.read(r,!0),()=>St(r)}function Mx(e){window.MotionDebug&&window.MotionDebug.record(e)}function Dx(e){return e instanceof SVGElement&&e.tagName!=="svg"}function _x(e,t,n){const r=je(e)?e:er(e);return r.start(au("",r,t,n)),r.animation}const fd=["","X","Y","Z"],Vx={visibility:"hidden"},pd=1e3;let Ix=0;const rn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function fm({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},a=t==null?void 0:t()){this.id=Ix++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rn.totalNodes=rn.resolvedTargetDeltas=rn.recalculatedProjection=0,this.nodes.forEach(Fx),this.nodes.forEach(Wx),this.nodes.forEach(Hx),this.nodes.forEach(Bx),Mx(rn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Lx)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new cu),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Dx(s),this.instance=s;const{layoutId:l,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let c;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=Nx(p,250),Ki.hasAnimatedSinceResize&&(Ki.hasAnimatedSinceResize=!1,this.nodes.forEach(md))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&d&&(l||u)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:p,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||d.getDefaultTransition()||Xx,{onLayoutAnimationStart:S,onLayoutAnimationComplete:m}=d.getProps(),f=!this.targetLayout||!dm(this.targetLayout,v)||g,h=!p&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||p&&(f||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,h);const w={...su(x,"layout"),onPlay:S,onComplete:m};(d.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else p||md(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,St(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Kx),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const c=this.path[d];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(hd);return}this.isUpdating||this.nodes.forEach(bx),this.isUpdating=!1,this.nodes.forEach($x),this.nodes.forEach(Ox),this.nodes.forEach(zx),this.clearAllSnapshots();const a=performance.now();me.delta=Wt(0,1e3/60,a-me.timestamp),me.timestamp=a,me.isProcessing=!0,Rs.update.process(me),Rs.preRender.process(me),Rs.render.process(me),me.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Ux),this.sharedNodes.forEach(Gx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,U.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){U.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!cm(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,d=u!==this.prevTransformTemplateValue;s&&(a||nn(this.latestValues)||d)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),Zx(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ee();const a=s.measureViewportBox(),{scroll:l}=this.root;return l&&(At(a.x,l.offset.x),At(a.y,l.offset.y)),a}removeElementScroll(s){const a=ee();Fe(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:d,options:c}=u;if(u!==this.root&&d&&c.layoutScroll){if(d.isRoot){Fe(a,s);const{scroll:p}=this.root;p&&(At(a.x,-p.offset.x),At(a.y,-p.offset.y))}At(a.x,d.offset.x),At(a.y,d.offset.y)}}return a}applyTransform(s,a=!1){const l=ee();Fe(l,s);for(let u=0;u<this.path.length;u++){const d=this.path[u];!a&&d.options.layoutScroll&&d.scroll&&d!==d.root&&Fn(l,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),nn(d.latestValues)&&Fn(l,d.latestValues)}return nn(this.latestValues)&&Fn(l,this.latestValues),l}removeTransform(s){const a=ee();Fe(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!nn(u.latestValues))continue;ba(u.latestValues)&&u.updateSnapshot();const d=ee(),c=u.measurePageBox();Fe(d,c),ld(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,d)}return nn(this.latestValues)&&ld(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==me.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:c,layoutId:p}=this.options;if(!(!this.layout||!(c||p))){if(this.resolvedRelativeTargetAt=me.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Fe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ee(),this.targetWithTransforms=ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),q1(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Fe(this.target,this.layout.layoutBox),im(this.target,this.targetDelta)):Fe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Dr(this.relativeTargetOrigin,this.target,g.target),Fe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ba(this.parent.latestValues)||rm(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===me.timestamp&&(u=!1),u)return;const{layout:d,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||c))return;Fe(this.layoutCorrected,this.layout.layoutBox);const p=this.treeScale.x,g=this.treeScale.y;ax(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:v}=a;if(!v){this.projectionTransform&&(this.projectionDelta=zn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=zn(),this.projectionDeltaWithTransform=zn());const x=this.projectionTransform;Mr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=dd(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==p||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),rn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},d={...this.latestValues},c=zn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=ee(),g=l?l.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,S=this.getStack(),m=!S||S.members.length<=1,f=!!(x&&!m&&this.options.crossfade===!0&&!this.path.some(Yx));this.animationProgress=0;let h;this.mixTargetDelta=w=>{const k=w/1e3;gd(c.x,s.x,k),gd(c.y,s.y,k),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Dr(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Qx(this.relativeTarget,this.relativeTargetOrigin,p,k),h&&Rx(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=ee()),Fe(h,this.relativeTarget)),x&&(this.animationValues=d,Sx(d,u,this.latestValues,k,f,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(St(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=U.update(()=>{Ki.hasAnimatedSinceResize=!0,this.currentAnimation=_x(0,pd,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(pd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:d}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&pm(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ee();const c=Ve(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+c;const p=Ve(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+p}Fe(a,l),Fn(a,d),Mr(this.projectionDeltaWithTransform,this.layoutCorrected,a,d)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Ax),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let d=0;d<fd.length;d++){const c="rotate"+fd[d];l[c]&&(u[c]=l[c],s.setStaticValue(c,0))}s.render();for(const d in u)s.setStaticValue(d,u[d]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Vx;const u={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Hi(s==null?void 0:s.pointerEvents)||"",u.transform=d?d(this.latestValues,""):"none",u;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Hi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!nn(this.latestValues)&&(x.transform=d?d({},""):"none",this.hasProjected=!1),x}const p=c.animationValues||c.latestValues;this.applyTransformsToTarget(),u.transform=dd(this.projectionDeltaWithTransform,this.treeScale,p),d&&(u.transform=d(p,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,c.animationValues?u.opacity=c===this?(l=(a=p.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:u.opacity=c===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const x in vo){if(p[x]===void 0)continue;const{correct:S,applyTo:m}=vo[x],f=u.transform==="none"?p[x]:S(p[x],c);if(m){const h=m.length;for(let w=0;w<h;w++)u[m[w]]=f}else u[x]=f}return this.options.layoutId&&(u.pointerEvents=c===this?Hi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(hd),this.root.sharedNodes.clear()}}}function Ox(e){e.updateLayout()}function zx(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?Be(c=>{const p=s?n.measuredBox[c]:n.layoutBox[c],g=Ve(p);p.min=r[c].min,p.max=p.min+g}):pm(o,n.layoutBox,r)&&Be(c=>{const p=s?n.measuredBox[c]:n.layoutBox[c],g=Ve(r[c]);p.max=p.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+g)});const a=zn();Mr(a,r,n.layoutBox);const l=zn();s?Mr(l,e.applyTransform(i,!0),n.measuredBox):Mr(l,r,n.layoutBox);const u=!cm(a);let d=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:p,layout:g}=c;if(p&&g){const v=ee();Dr(v,n.layoutBox,p.layoutBox);const x=ee();Dr(x,r,g.layoutBox),dm(v,x)||(d=!0),c.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Fx(e){rn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Bx(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Ux(e){e.clearSnapshot()}function hd(e){e.clearMeasurements()}function bx(e){e.isLayoutDirty=!1}function $x(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function md(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Wx(e){e.resolveTargetDelta()}function Hx(e){e.calcProjection()}function Kx(e){e.resetRotation()}function Gx(e){e.removeLeadSnapshot()}function gd(e,t,n){e.translate=G(t.translate,0,n),e.scale=G(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function yd(e,t,n,r){e.min=G(t.min,n.min,r),e.max=G(t.max,n.max,r)}function Qx(e,t,n,r){yd(e.x,t.x,n.x,r),yd(e.y,t.y,n.y,r)}function Yx(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Xx={duration:.45,ease:[.4,0,.1,1]},vd=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),xd=vd("applewebkit/")&&!vd("chrome/")?Math.round:J;function wd(e){e.min=xd(e.min),e.max=xd(e.max)}function Zx(e){wd(e.x),wd(e.y)}function pm(e,t,n){return e==="position"||e==="preserve-aspect"&&!Ba(cd(t),cd(n),.2)}const qx=fm({attachResizeListener:(e,t)=>pt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),zs={current:void 0},hm=fm({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!zs.current){const e=new qx({});e.mount(window),e.setOptions({layoutScroll:!0}),zs.current=e}return zs.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Jx={pan:{Feature:mx},drag:{Feature:hx,ProjectionNode:hm,MeasureLayout:am}},ew=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function tw(e){const t=ew.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Wa(e,t,n=1){const[r,i]=tw(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return Xh(s)?parseFloat(s):s}else return Da(i)?Wa(i,t,n+1):i}function nw(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!Da(o))return;const s=Wa(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!Da(o))continue;const s=Wa(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const rw=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),mm=e=>rw.has(e),iw=e=>Object.keys(e).some(mm),Sd=e=>e===Sn||e===N,kd=(e,t)=>parseFloat(e.split(", ")[t]),Cd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return kd(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?kd(o[1],e):0}},ow=new Set(["x","y","z"]),sw=si.filter(e=>!ow.has(e));function aw(e){const t=[];return sw.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const tr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Cd(4,13),y:Cd(5,14)};tr.translateX=tr.x;tr.translateY=tr.y;const lw=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=tr[u](r,o)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const d=t.getValue(u);d&&d.jump(a[u]),e[u]=tr[u](l,o)}),e},uw=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(mm);let o=[],s=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let d=n[l],c=hr(d);const p=t[l];let g;if(wo(p)){const v=p.length,x=p[0]===null?1:0;d=p[x],c=hr(d);for(let S=x;S<v&&p[S]!==null;S++)g?tu(hr(p[S])===g):g=hr(p[S])}else g=hr(p);if(c!==g)if(Sd(c)&&Sd(g)){const v=u.get();typeof v=="string"&&u.set(parseFloat(v)),typeof p=="string"?t[l]=parseFloat(p):Array.isArray(p)&&g===N&&(t[l]=p.map(parseFloat))}else c!=null&&c.transform&&(g!=null&&g.transform)&&(d===0||p===0)?d===0?u.set(g.transform(d)):t[l]=c.transform(p):(s||(o=aw(e),s=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(p))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=lw(t,e,a);return o.length&&o.forEach(([d,c])=>{e.getValue(d).set(c)}),e.render(),Qo&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function cw(e,t,n,r){return iw(t)?uw(e,t,n,r):{target:t,transitionEnd:r}}const dw=(e,t,n,r)=>{const i=nw(e,t,r);return t=i.target,r=i.transitionEnd,cw(e,t,n,r)},Ha={current:null},gm={current:!1};function fw(){if(gm.current=!0,!!Qo)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ha.current=e.matches;e.addListener(t),t()}else Ha.current=!1}function pw(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(je(o))e.addValue(i,o),Po(r)&&r.add(i);else if(je(s))e.addValue(i,er(o,{owner:e})),Po(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=e.getStaticValue(i);e.addValue(i,er(a!==void 0?a:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Pd=new WeakMap,ym=Object.keys(qr),hw=ym.length,Ed=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],mw=Kl.length;class gw{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>U.render(this.render,!1,!0);const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=Xo(n),this.isVariantNode=th(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...d}=this.scrapeMotionValuesFromProps(n,{});for(const c in d){const p=d[c];a[c]!==void 0&&je(p)&&(p.set(a[c],!1),Po(u)&&u.add(c))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,Pd.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),gm.current||fw(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ha.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Pd.delete(this.current),this.projection&&this.projection.unmount(),St(this.notifyUpdate),St(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=wn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&U.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,a;for(let l=0;l<hw;l++){const u=ym[l],{isEnabled:d,Feature:c,ProjectionNode:p,MeasureLayout:g}=qr[u];p&&(s=p),d(n)&&(!this.features[u]&&c&&(this.features[u]=new c(this)),g&&(a=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:d,dragConstraints:c,layoutScroll:p,layoutRoot:g}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!d||c&&In(c),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:p,layoutRoot:g})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ee()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Ed.length;r++){const i=Ed[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=pw(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<mw;r++){const i=Kl[r],o=this.props[i];(Zr(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=er(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=eu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!je(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new cu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class vm extends gw{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=D1(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){N1(this,r,s);const a=dw(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function yw(e){return window.getComputedStyle(e)}class vw extends vm{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(wn.has(n)){const r=ou(n);return r&&r.default||0}else{const r=yw(t),i=(oh(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return om(t,n)}build(t,n,r,i){Yl(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Jl(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;je(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){dh(t,n,r,i)}}class xw extends vm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(wn.has(n)){const r=ou(n);return r&&r.default||0}return n=fh.has(n)?n:Wl(n),t.getAttribute(n)}measureInstanceViewportBox(){return ee()}scrapeMotionValuesFromProps(t,n){return hh(t,n)}build(t,n,r,i){Zl(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){ph(t,n,r,i)}mount(t){this.isSVGTag=ql(t.tagName),super.mount(t)}}const ww=(e,t)=>Ql(e)?new xw(t,{enableHardwareAcceleration:!1}):new vw(t,{enableHardwareAcceleration:!0}),Sw={layout:{ProjectionNode:hm,MeasureLayout:am}},kw={...Q1,...h0,...Jx,...Sw},ot=Cv((e,t)=>t0(e,t,kw,ww));function xm(){const e=E.useRef(!1);return $l(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Cw(){const e=xm(),[t,n]=E.useState(0),r=E.useCallback(()=>{e.current&&n(t+1)},[t]);return[E.useCallback(()=>U.postRender(r),[r]),t]}class Pw extends E.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Ew({children:e,isPresent:t}){const n=E.useId(),r=E.useRef(null),i=E.useRef({width:0,height:0,top:0,left:0});return E.useInsertionEffect(()=>{const{width:o,height:s,top:a,left:l}=i.current;if(t||!r.current||!o||!s)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${s}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),E.createElement(Pw,{isPresent:t,childRef:r,sizeRef:i},E.cloneElement(e,{ref:r}))}const Fs=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:o,mode:s})=>{const a=mh(Tw),l=E.useId(),u=E.useMemo(()=>({id:l,initial:t,isPresent:n,custom:i,onExitComplete:d=>{a.set(d,!0);for(const c of a.values())if(!c)return;r&&r()},register:d=>(a.set(d,!1),()=>a.delete(d))}),o?void 0:[n]);return E.useMemo(()=>{a.forEach((d,c)=>a.set(c,!1))},[n]),E.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),s==="popLayout"&&(e=E.createElement(Ew,{isPresent:n},e)),E.createElement(Go.Provider,{value:u},e)};function Tw(){return new Map}function Rw(e){return E.useEffect(()=>()=>e(),[])}const on=e=>e.key||"";function Aw(e,t){e.forEach(n=>{const r=on(n);t.set(r,n)})}function jw(e){const t=[];return E.Children.forEach(e,n=>{E.isValidElement(n)&&t.push(n)}),t}const Lw=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:s="sync"})=>{const a=E.useContext(Gl).forceRender||Cw()[0],l=xm(),u=jw(e);let d=u;const c=E.useRef(new Map).current,p=E.useRef(d),g=E.useRef(new Map).current,v=E.useRef(!0);if($l(()=>{v.current=!1,Aw(u,g),p.current=d}),Rw(()=>{v.current=!0,g.clear(),c.clear()}),v.current)return E.createElement(E.Fragment,null,d.map(f=>E.createElement(Fs,{key:on(f),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:o,mode:s},f)));d=[...d];const x=p.current.map(on),S=u.map(on),m=x.length;for(let f=0;f<m;f++){const h=x[f];S.indexOf(h)===-1&&!c.has(h)&&c.set(h,void 0)}return s==="wait"&&c.size&&(d=[]),c.forEach((f,h)=>{if(S.indexOf(h)!==-1)return;const w=g.get(h);if(!w)return;const k=x.indexOf(h);let T=f;if(!T){const C=()=>{c.delete(h);const R=Array.from(g.keys()).filter(L=>!S.includes(L));if(R.forEach(L=>g.delete(L)),p.current=u.filter(L=>{const M=on(L);return M===h||R.includes(M)}),!c.size){if(l.current===!1)return;a(),r&&r()}};T=E.createElement(Fs,{key:on(w),isPresent:!1,onExitComplete:C,custom:t,presenceAffectsLayout:o,mode:s},w),c.set(h,T)}d.splice(k,0,T)}),d=d.map(f=>{const h=f.key;return c.has(h)?f:E.createElement(Fs,{key:on(f),isPresent:!0,presenceAffectsLayout:o,mode:s},f)}),E.createElement(E.Fragment,null,c.size?d:d.map(f=>E.cloneElement(f)))};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Nw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),ne=(e,t)=>{const n=E.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:a="",children:l,...u},d)=>E.createElement("svg",{ref:d,...Nw,width:i,height:i,stroke:r,strokeWidth:s?Number(o)*24/Number(i):o,className:["lucide",`lucide-${Mw(e)}`,a].join(" "),...u},[...t.map(([c,p])=>E.createElement(c,p)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dw=ne("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wm=ne("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _w=ne("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Td=ne("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rd=ne("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=ne("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vw=ne("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iw=ne("Keyboard",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",ry:"2",key:"15u882"}],["path",{d:"M6 8h.001",key:"1ej0i3"}],["path",{d:"M10 8h.001",key:"1x2st2"}],["path",{d:"M14 8h.001",key:"1vkmyp"}],["path",{d:"M18 8h.001",key:"kfsenl"}],["path",{d:"M8 12h.001",key:"1sjpby"}],["path",{d:"M12 12h.001",key:"al75ts"}],["path",{d:"M16 12h.001",key:"931bgk"}],["path",{d:"M7 16h10",key:"wp8him"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ow=ne("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sm=ne("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const To=ne("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zw=ne("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fw=ne("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bw=ne("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ka=ne("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uw=ne("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bw=ne("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ga=ne("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const km=ne("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ro=ne("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function $w(){const{state:e,startRecording:t,stopRecording:n,dispatch:r}=oi(),{recordingState:i,config:o}=e,s=async d=>{i.isRecording?await n():await t(d)},a=async()=>{var d;(d=window.electronAPI)!=null&&d.showSettings?await window.electronAPI.showSettings():r({type:"SHOW_SETTINGS_WINDOW",payload:!0})},l=async()=>{await window.electronAPI.minimizeToTray()},u=o.azureOpenAI.apiKey&&o.azureSpeech.apiKey;return y.jsxs(ot.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},transition:{duration:.3},className:"main-window",children:[y.jsxs("div",{className:"title-bar",children:[y.jsxs("div",{className:"title",children:[y.jsx(Ro,{size:20}),y.jsx("span",{children:"SpeechPilot"})]}),y.jsx("div",{className:"title-actions",children:y.jsx("button",{className:"btn btn-icon",onClick:a,title:"設定",children:y.jsx(Ka,{size:16})})})]}),y.jsx("div",{className:"main-content",children:u?y.jsxs("div",{className:"recording-controls",children:[y.jsxs("div",{className:"status-display",children:[y.jsx("div",{className:`status-indicator ${i.status}`,children:i.isRecording?y.jsx(Sm,{size:24}):y.jsx(To,{size:24})}),y.jsxs("div",{className:"status-text",children:[y.jsx("h3",{children:i.isRecording?"錄音中...":"準備就緒"}),y.jsx("p",{children:i.isRecording?`${i.mode==="ai"?"AI 智能":"直接轉錄"} 模式`:"按下按鈕或使用快捷鍵開始錄音"})]})]}),y.jsxs("div",{className:"recording-buttons",children:[y.jsxs(ot.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:`btn btn-recording ${i.isRecording&&i.mode==="ai"?"btn-danger":"btn-primary"}`,onClick:()=>s("ai"),disabled:i.isRecording&&i.mode!=="ai",children:[y.jsx(Ro,{size:20}),y.jsx("span",{children:i.isRecording&&i.mode==="ai"?"停止錄音":"AI 智能模式"}),y.jsx("small",{children:o.hotkeys.aiMode})]}),y.jsxs(ot.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:`btn btn-recording ${i.isRecording&&i.mode==="direct"?"btn-danger":"btn-secondary"}`,onClick:()=>s("direct"),disabled:i.isRecording&&i.mode!=="direct",children:[y.jsx(Ga,{size:20}),y.jsx("span",{children:i.isRecording&&i.mode==="direct"?"停止錄音":"直接轉錄"}),y.jsx("small",{children:o.hotkeys.directMode})]})]}),i.result&&y.jsxs(ot.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"result-display",children:[y.jsx("h4",{children:"最後結果："}),y.jsx("p",{children:i.result})]})]}):y.jsxs("div",{className:"setup-prompt",children:[y.jsx("div",{className:"setup-icon",children:y.jsx(Ka,{size:48})}),y.jsx("h2",{children:"歡迎使用 SpeechPilot"}),y.jsx("p",{children:"請先設定 Azure API 金鑰以開始使用"}),y.jsxs("div",{className:"setup-checklist",children:[y.jsxs("div",{className:`checklist-item ${o.azureOpenAI.apiKey?"completed":""}`,children:[y.jsx("span",{children:"Azure OpenAI API 金鑰"}),o.azureOpenAI.apiKey&&y.jsx("span",{className:"checkmark",children:"✓"})]}),y.jsxs("div",{className:`checklist-item ${o.azureSpeech.apiKey?"completed":""}`,children:[y.jsx("span",{children:"Azure Speech API 金鑰"}),o.azureSpeech.apiKey&&y.jsx("span",{className:"checkmark",children:"✓"})]})]}),y.jsx("button",{className:"btn btn-primary",onClick:a,children:u?"修改設定":"開始設定"})]})}),y.jsxs("div",{className:"bottom-actions",children:[y.jsx("button",{className:"btn btn-secondary btn-sm",onClick:l,title:"最小化到系統托盤",children:"最小化到托盤"}),!1,y.jsx("div",{className:"version-info",children:"v1.0.0 MVP"})]}),y.jsx("style",{children:`
        .main-window {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          flex-direction: column;
          color: white;
        }

        .title-bar {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
        }

        .title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          font-size: 16px;
        }

        .title-actions {
          display: flex;
          gap: 8px;
        }

        .btn-icon {
          padding: 8px;
          background: rgba(255, 255, 255, 0.1);
          border: none;
          border-radius: 6px;
          color: white;
          cursor: pointer;
          transition: background 0.2s ease;
        }

        .btn-icon:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .main-content {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
        }

        .setup-prompt {
          text-align: center;
          max-width: 300px;
        }

        .setup-icon {
          margin-bottom: 16px;
          opacity: 0.8;
        }

        .setup-prompt h2 {
          margin-bottom: 8px;
          font-size: 24px;
        }

        .setup-prompt p {
          margin-bottom: 16px;
          opacity: 0.9;
          line-height: 1.5;
        }

        .setup-checklist {
          margin-bottom: 24px;
          text-align: left;
          max-width: 250px;
        }

        .checklist-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          margin-bottom: 8px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 6px;
          font-size: 14px;
        }

        .checklist-item.completed {
          background: rgba(40, 167, 69, 0.2);
          border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .checkmark {
          color: #28a745;
          font-weight: bold;
          font-size: 16px;
        }

        .recording-controls {
          text-align: center;
          width: 100%;
          max-width: 320px;
        }

        .status-display {
          margin-bottom: 32px;
        }

        .status-indicator {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 16px;
          transition: all 0.3s ease;
        }

        .status-indicator.idle {
          background: rgba(255, 255, 255, 0.2);
        }

        .status-indicator.recording {
          background: #dc3545;
          animation: pulse 2s infinite;
        }

        .status-indicator.processing {
          background: #ffc107;
          animation: spin 1s linear infinite;
        }

        .status-indicator.completed {
          background: #28a745;
        }

        .status-indicator.error {
          background: #dc3545;
        }

        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .status-text h3 {
          margin-bottom: 4px;
          font-size: 20px;
        }

        .status-text p {
          opacity: 0.9;
          font-size: 14px;
        }

        .recording-buttons {
          display: flex;
          flex-direction: column;
          gap: 12px;
          margin-bottom: 24px;
        }

        .btn-recording {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          padding: 16px;
          min-height: 80px;
          font-size: 16px;
          font-weight: 600;
        }

        .btn-recording small {
          font-size: 12px;
          opacity: 0.8;
          font-weight: normal;
        }

        .result-display {
          background: rgba(255, 255, 255, 0.1);
          padding: 16px;
          border-radius: 8px;
          text-align: left;
        }

        .result-display h4 {
          margin-bottom: 8px;
          font-size: 14px;
          opacity: 0.9;
        }

        .result-display p {
          font-size: 14px;
          line-height: 1.4;
        }

        .bottom-actions {
          padding: 16px;
          text-align: center;
          display: flex;
          gap: 8px;
          justify-content: center;
          flex-wrap: wrap;
        }

        .btn-sm {
          padding: 6px 12px;
          font-size: 12px;
        }

        .version-info {
          margin-top: 8px;
          font-size: 10px;
          opacity: 0.6;
          color: white;
        }
      `})]})}function Ww(){console.log("🎙️ RecordingWindow: Component rendering");const{state:e,stopRecording:t,dispatch:n}=oi(),{recordingState:r}=e,[i,o]=E.useState(0);console.log("🎙️ RecordingWindow: Current state:",{isRecording:r.isRecording,mode:r.mode,status:r.status,duration:r.duration}),E.useEffect(()=>{let c=null;return r.isRecording?c=setInterval(()=>{o(p=>p+1)},1e3):o(0),()=>{c&&clearInterval(c)}},[r.isRecording]),E.useEffect(()=>{const c=p=>{p.key==="Escape"?l():(p.key===" "||p.key==="Enter")&&(p.preventDefault(),r.isRecording&&a())};return document.addEventListener("keydown",c),()=>{document.removeEventListener("keydown",c)}},[]);const s=c=>{const p=Math.floor(c/60),g=c%60;return`${p.toString().padStart(2,"0")}:${g.toString().padStart(2,"0")}`},a=async()=>{await t()},l=async()=>{var c;r.isRecording&&await t(),(c=window.electronAPI)!=null&&c.hideRecordingWindow?await window.electronAPI.hideRecordingWindow():n({type:"SHOW_RECORDING_WINDOW",payload:!1})},u=()=>{switch(r.status){case"recording":return"正在錄音...";case"processing":return"正在處理...";case"completed":return"處理完成";case"error":return"發生錯誤";default:return"準備就緒"}},d=()=>{switch(r.status){case"recording":return y.jsx(To,{size:32});case"processing":return y.jsx(Ow,{size:32,className:"spinning"});case"completed":return r.mode==="ai"?y.jsx(Ro,{size:32}):y.jsx(Ga,{size:32});case"error":return y.jsx(Sm,{size:32});default:return y.jsx(To,{size:32})}};return console.log("🎙️ RecordingWindow: Rendering component with state:",{isRecording:r==null?void 0:r.isRecording,mode:r==null?void 0:r.mode,status:r==null?void 0:r.status}),y.jsxs(ot.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},className:"recording-window",children:[y.jsxs("div",{className:"recording-header",children:[y.jsx("div",{className:"mode-indicator",children:r.mode==="ai"?y.jsxs(y.Fragment,{children:[y.jsx(Ro,{size:16}),y.jsx("span",{children:"AI 智能模式"})]}):y.jsxs(y.Fragment,{children:[y.jsx(Ga,{size:16}),y.jsx("span",{children:"直接轉錄模式"})]})}),y.jsx("button",{className:"close-btn",onClick:l,title:"關閉",children:"×"})]}),y.jsxs("div",{className:"recording-content",children:[y.jsx("div",{className:`status-circle ${r.status}`,children:d()}),y.jsxs("div",{className:"status-info",children:[y.jsx("h3",{children:u()}),r.isRecording&&y.jsx("div",{className:"duration",children:s(i)})]}),r.status==="processing"&&y.jsx("div",{className:"processing-info",children:y.jsxs("p",{children:["正在",r.mode==="ai"?"AI 分析":"使用 gpt-4o-mini-transcribe 轉錄","中..."]})}),r.result&&r.status==="completed"&&y.jsx(ot.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"result-preview",children:y.jsxs("p",{children:[r.result.substring(0,100),r.result.length>100?"...":""]})}),r.error&&r.status==="error"&&y.jsx(ot.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"error-message",children:y.jsx("p",{children:r.error})})]}),y.jsx("div",{className:"recording-actions",children:r.isRecording?y.jsxs(ot.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn btn-danger",onClick:a,children:[y.jsx(Uw,{size:16}),"停止錄音"]}):r.status==="completed"&&y.jsx("button",{className:"btn btn-secondary",onClick:l,children:"關閉"})}),y.jsx("style",{children:`
        .recording-window {
          width: 400px;
          height: 300px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
          display: flex;
          flex-direction: column;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
          overflow: hidden;
        }

        .recording-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
        }

        .mode-indicator {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          font-weight: 600;
          color: #495057;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: 18px;
          cursor: pointer;
          padding: 4px 8px;
          border-radius: 4px;
          color: #6c757d;
          transition: background-color 0.2s ease;
        }

        .close-btn:hover {
          background: #e9ecef;
          color: #495057;
        }

        .recording-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 20px;
          text-align: center;
        }

        .status-circle {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 16px;
          transition: all 0.3s ease;
        }

        .status-circle.idle {
          background: #f8f9fa;
          color: #6c757d;
        }

        .status-circle.recording {
          background: #dc3545;
          color: white;
          animation: pulse 2s infinite;
        }

        .status-circle.processing {
          background: #ffc107;
          color: white;
        }

        .status-circle.completed {
          background: #28a745;
          color: white;
        }

        .status-circle.error {
          background: #dc3545;
          color: white;
        }

        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }

        .spinning {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        .status-info h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          color: #212529;
        }

        .duration {
          font-size: 24px;
          font-weight: 700;
          color: #dc3545;
          font-family: 'Courier New', monospace;
        }

        .processing-info {
          margin-top: 12px;
        }

        .processing-info p {
          margin: 0;
          font-size: 14px;
          color: #6c757d;
        }

        .result-preview {
          margin-top: 16px;
          padding: 12px;
          background: #f8f9fa;
          border-radius: 6px;
          max-width: 100%;
        }

        .result-preview p {
          margin: 0;
          font-size: 12px;
          color: #495057;
          line-height: 1.4;
        }

        .error-message {
          margin-top: 16px;
          padding: 12px;
          background: #f8d7da;
          border-radius: 6px;
          max-width: 100%;
        }

        .error-message p {
          margin: 0;
          font-size: 12px;
          color: #721c24;
          line-height: 1.4;
        }

        .recording-actions {
          padding: 16px;
          text-align: center;
          border-top: 1px solid #e9ecef;
        }

        .btn {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .btn-danger {
          background: #dc3545;
          color: white;
        }

        .btn-danger:hover {
          background: #c82333;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
        }

        .btn-secondary:hover {
          background: #5a6268;
        }
      `})]})}function Hw(){const{state:e,updateConfig:t,loadAudioDevices:n,showNotification:r,dispatch:i}=oi(),[o,s]=E.useState(e.config),[a,l]=E.useState(!1),[u,d]=E.useState(!1),[c,p]=E.useState("api");E.useEffect(()=>{s(e.config),n()},[e.config,n]),E.useEffect(()=>{const f=h=>{h.key==="Escape"&&v()};return document.addEventListener("keydown",f),()=>{document.removeEventListener("keydown",f)}},[]);const g=async()=>{try{await t(o),r({type:"success",message:"設定已儲存",duration:2e3})}catch{r({type:"error",message:"設定儲存失敗",duration:3e3})}},v=async()=>{var f;(f=window.electronAPI)!=null&&f.hideSettings?await window.electronAPI.hideSettings():i({type:"SHOW_SETTINGS_WINDOW",payload:!1})},x=async()=>{d(!0);try{await new Promise(f=>setTimeout(f,2e3)),r({type:"success",message:"API 連接測試成功",duration:3e3})}catch{r({type:"error",message:"API 連接測試失敗",duration:3e3})}finally{d(!1)}},S=(f,h)=>{const w=f.split("."),k={...o};let T=k;for(let C=0;C<w.length-1;C++)T=T[w[C]];T[w[w.length-1]]=h,s(k)},m=[{id:"api",label:"API 設定",icon:Vw},{id:"hotkeys",label:"快捷鍵",icon:Iw},{id:"audio",label:"音頻設定",icon:To},{id:"ui",label:"介面設定",icon:zw}];return y.jsxs(ot.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.2},className:"settings-window",children:[y.jsxs("div",{className:"settings-header",children:[y.jsx("h1",{children:"設定"}),y.jsx("button",{className:"close-btn",onClick:v,title:"關閉",children:y.jsx(km,{size:20})})]}),y.jsxs("div",{className:"settings-content",children:[y.jsx("div",{className:"settings-sidebar",children:m.map(f=>{const h=f.icon;return y.jsxs("button",{className:`tab-btn ${c===f.id?"active":""}`,onClick:()=>p(f.id),children:[y.jsx(h,{size:16}),y.jsx("span",{children:f.label})]},f.id)})}),y.jsxs("div",{className:"settings-main",children:[c==="api"&&y.jsxs("div",{className:"settings-section",children:[y.jsx("h2",{children:"API 設定"}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"Azure OpenAI 端點"}),y.jsx("input",{type:"text",className:"input",value:o.azureOpenAI.endpoint,onChange:f=>S("azureOpenAI.endpoint",f.target.value),placeholder:"https://your-resource.openai.azure.com/"})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"Azure OpenAI API 金鑰"}),y.jsxs("div",{className:"input-with-toggle",children:[y.jsx("input",{type:a?"text":"password",className:"input",value:o.azureOpenAI.apiKey,onChange:f=>S("azureOpenAI.apiKey",f.target.value),placeholder:"輸入您的 API 金鑰"}),y.jsx("button",{type:"button",className:"toggle-btn",onClick:()=>l(!a),children:a?y.jsx(Td,{size:16}):y.jsx(Rd,{size:16})})]})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"Azure OpenAI 模型（AI 智能模式）"}),y.jsx("input",{type:"text",className:"input",value:o.azureOpenAI.model,onChange:f=>S("azureOpenAI.model",f.target.value),placeholder:"gpt-4o-mini-audio-preview"})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"轉錄模型（直接轉錄模式）"}),y.jsx("input",{type:"text",className:"input",value:o.azureOpenAI.transcribeModel,onChange:f=>S("azureOpenAI.transcribeModel",f.target.value),placeholder:"gpt-4o-mini-transcribe"})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"Azure Speech 區域"}),y.jsx("input",{type:"text",className:"input",value:o.azureSpeech.region,onChange:f=>S("azureSpeech.region",f.target.value),placeholder:"eastus"})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"Azure Speech API 金鑰"}),y.jsxs("div",{className:"input-with-toggle",children:[y.jsx("input",{type:a?"text":"password",className:"input",value:o.azureSpeech.apiKey,onChange:f=>S("azureSpeech.apiKey",f.target.value),placeholder:"輸入您的 API 金鑰"}),y.jsx("button",{type:"button",className:"toggle-btn",onClick:()=>l(!a),children:a?y.jsx(Td,{size:16}):y.jsx(Rd,{size:16})})]})]}),y.jsxs("button",{className:"btn btn-secondary",onClick:x,disabled:u,children:[y.jsx(bw,{size:16}),u?"測試中...":"測試連接"]})]}),c==="hotkeys"&&y.jsxs("div",{className:"settings-section",children:[y.jsx("h2",{children:"快捷鍵設定"}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"AI 智能模式"}),y.jsx("input",{type:"text",className:"input",value:o.hotkeys.aiMode,onChange:f=>S("hotkeys.aiMode",f.target.value),placeholder:"CommandOrControl+Shift+C"}),y.jsx("small",{children:"例如：CommandOrControl+Shift+C"})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"直接轉錄模式"}),y.jsx("input",{type:"text",className:"input",value:o.hotkeys.directMode,onChange:f=>S("hotkeys.directMode",f.target.value),placeholder:"CommandOrControl+Shift+V"}),y.jsx("small",{children:"例如：CommandOrControl+Shift+V"})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"快捷鍵模式"}),y.jsxs("select",{className:"input",value:o.recording.hotkeyMode,onChange:f=>S("recording.hotkeyMode",f.target.value),children:[y.jsx("option",{value:"toggle",children:"切換模式（按一下開始，再按一下停止）"}),y.jsx("option",{value:"press",children:"按住模式（按住錄音，放開停止）"})]})]})]}),c==="audio"&&y.jsxs("div",{className:"settings-section",children:[y.jsx("h2",{children:"音頻設定"}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"錄音設備"}),y.jsxs("select",{className:"input",value:o.recording.deviceId||"default",onChange:f=>S("recording.deviceId",f.target.value),children:[y.jsx("option",{value:"default",children:"預設設備"}),e.audioDevices.map(f=>y.jsx("option",{value:f.deviceId,children:f.label},f.deviceId))]})]})]}),c==="ui"&&y.jsxs("div",{className:"settings-section",children:[y.jsx("h2",{children:"介面設定"}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"主題"}),y.jsxs("select",{className:"input",value:o.ui.theme,onChange:f=>S("ui.theme",f.target.value),children:[y.jsx("option",{value:"light",children:"淺色主題"}),y.jsx("option",{value:"dark",children:"深色主題"})]})]}),y.jsxs("div",{className:"form-group",children:[y.jsx("label",{children:"語言"}),y.jsxs("select",{className:"input",value:o.ui.language,onChange:f=>S("ui.language",f.target.value),children:[y.jsx("option",{value:"zh-TW",children:"繁體中文"}),y.jsx("option",{value:"en-US",children:"English"})]})]})]})]})]}),y.jsxs("div",{className:"settings-footer",children:[y.jsx("button",{className:"btn btn-secondary",onClick:v,children:"取消"}),y.jsxs("button",{className:"btn btn-primary",onClick:g,children:[y.jsx(Bw,{size:16}),"儲存設定"]})]}),y.jsx("style",{children:`
        .settings-window {
          width: 100%;
          height: 100%;
          background: white;
          display: flex;
          flex-direction: column;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .settings-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid #e9ecef;
          background: #f8f9fa;
        }

        .settings-header h1 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #212529;
        }

        .close-btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 8px;
          border-radius: 6px;
          color: #6c757d;
          transition: all 0.2s ease;
        }

        .close-btn:hover {
          background: #e9ecef;
          color: #495057;
        }

        .settings-content {
          flex: 1;
          display: flex;
          overflow: hidden;
        }

        .settings-sidebar {
          width: 200px;
          background: #f8f9fa;
          border-right: 1px solid #e9ecef;
          padding: 16px 0;
        }

        .tab-btn {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 20px;
          border: none;
          background: none;
          text-align: left;
          cursor: pointer;
          transition: all 0.2s ease;
          color: #495057;
          font-size: 14px;
        }

        .tab-btn:hover {
          background: #e9ecef;
        }

        .tab-btn.active {
          background: #667eea;
          color: white;
        }

        .settings-main {
          flex: 1;
          padding: 24px;
          overflow-y: auto;
        }

        .settings-section h2 {
          margin: 0 0 24px 0;
          font-size: 18px;
          font-weight: 600;
          color: #212529;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 6px;
          font-size: 14px;
          font-weight: 500;
          color: #495057;
        }

        .input {
          width: 100%;
          padding: 10px 12px;
          border: 1px solid #ced4da;
          border-radius: 6px;
          font-size: 14px;
          transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .input:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-with-toggle {
          position: relative;
        }

        .toggle-btn {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          color: #6c757d;
          border-radius: 4px;
          transition: color 0.2s ease;
        }

        .toggle-btn:hover {
          color: #495057;
        }

        .form-group small {
          display: block;
          margin-top: 4px;
          font-size: 12px;
          color: #6c757d;
        }

        .settings-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          padding: 16px 20px;
          border-top: 1px solid #e9ecef;
          background: #f8f9fa;
        }

        .btn {
          display: inline-flex;
          align-items: center;
          gap: 6px;
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-primary {
          background: #667eea;
          color: white;
        }

        .btn-primary:hover:not(:disabled) {
          background: #5a6fd8;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
        }

        .btn-secondary:hover:not(:disabled) {
          background: #5a6268;
        }
      `})]})}function Kw({notifications:e}){const{dispatch:t}=oi(),n=o=>{t({type:"REMOVE_NOTIFICATION",payload:o})},r=o=>{switch(o){case"success":return y.jsx(_w,{size:20});case"error":return y.jsx(Dw,{size:20});case"warning":return y.jsx(wm,{size:20});case"info":return y.jsx(Ad,{size:20});default:return y.jsx(Ad,{size:20})}},i=o=>{switch(o){case"success":return"notification-success";case"error":return"notification-error";case"warning":return"notification-warning";case"info":return"notification-info";default:return"notification-info"}};return y.jsxs("div",{className:"notification-container",children:[y.jsx(Lw,{children:e.map(o=>y.jsx(ot.div,{initial:{opacity:0,x:300,scale:.8},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:300,scale:.8},transition:{duration:.3,ease:"easeOut"},className:`notification ${i(o.type)}`,children:y.jsxs("div",{className:"notification-content",children:[y.jsx("div",{className:"notification-icon",children:r(o.type)}),y.jsx("div",{className:"notification-message",children:o.message}),y.jsx("button",{className:"notification-close",onClick:()=>n(o.id),"aria-label":"關閉通知",children:y.jsx(km,{size:16})})]})},o.id))}),y.jsx("style",{children:`
        .notification-container {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 1000;
          display: flex;
          flex-direction: column;
          gap: 8px;
          max-width: 400px;
        }

        .notification {
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          overflow: hidden;
          backdrop-filter: blur(10px);
        }

        .notification-content {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          gap: 12px;
        }

        .notification-icon {
          flex-shrink: 0;
          display: flex;
          align-items: center;
        }

        .notification-message {
          flex: 1;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
        }

        .notification-close {
          flex-shrink: 0;
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.2s ease;
        }

        .notification-close:hover {
          background: rgba(0, 0, 0, 0.1);
        }

        .notification-success {
          background: linear-gradient(135deg, #28a745, #20c997);
          color: white;
        }

        .notification-error {
          background: linear-gradient(135deg, #dc3545, #e74c3c);
          color: white;
        }

        .notification-warning {
          background: linear-gradient(135deg, #ffc107, #f39c12);
          color: #212529;
        }

        .notification-info {
          background: linear-gradient(135deg, #17a2b8, #3498db);
          color: white;
        }

        .notification-warning .notification-close:hover {
          background: rgba(0, 0, 0, 0.1);
        }

        .notification-success .notification-close:hover,
        .notification-error .notification-close:hover,
        .notification-info .notification-close:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 480px) {
          .notification-container {
            left: 20px;
            right: 20px;
            max-width: none;
          }
          
          .notification-content {
            padding: 10px 12px;
          }
          
          .notification-message {
            font-size: 13px;
          }
        }
      `})]})}class Gw extends E.Component{constructor(n){super(n);tt(this,"handleReload",()=>{window.location.reload()});tt(this,"handleOpenSettings",async()=>{try{await window.electronAPI.showSettings()}catch(n){console.error("Failed to open settings:",n)}});tt(this,"handleRestart",async()=>{try{await window.electronAPI.quitApp()}catch(n){console.error("Failed to restart app:",n)}});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorInfo:null}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r}),this.logErrorToService(n,r)}logErrorToService(n,r){console.error("Error logged:",{message:n.message,stack:n.stack,componentStack:r.componentStack})}render(){return this.state.hasError?y.jsxs("div",{className:"error-boundary",children:[y.jsxs("div",{className:"error-content",children:[y.jsx("div",{className:"error-icon",children:y.jsx(wm,{size:64})}),y.jsx("h1",{children:"糟糕！出現了錯誤"}),y.jsx("p",{className:"error-message",children:"應用程式遇到了意外錯誤。我們為此感到抱歉。"}),y.jsxs("div",{className:"error-actions",children:[y.jsxs("button",{className:"btn btn-primary",onClick:this.handleReload,children:[y.jsx(Fw,{size:16}),"重新載入"]}),y.jsxs("button",{className:"btn btn-secondary",onClick:this.handleOpenSettings,children:[y.jsx(Ka,{size:16}),"開啟設定"]}),y.jsx("button",{className:"btn btn-secondary",onClick:this.handleRestart,children:"重新啟動應用程式"})]}),!1]}),y.jsx("style",{children:`
            .error-boundary {
              width: 100vw;
              height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 20px;
            }

            .error-content {
              text-align: center;
              max-width: 500px;
              width: 100%;
            }

            .error-icon {
              margin-bottom: 24px;
              opacity: 0.8;
            }

            h1 {
              font-size: 32px;
              margin-bottom: 16px;
              font-weight: 700;
            }

            .error-message {
              font-size: 16px;
              line-height: 1.6;
              margin-bottom: 32px;
              opacity: 0.9;
            }

            .error-actions {
              display: flex;
              flex-direction: column;
              gap: 12px;
              margin-bottom: 32px;
            }

            .btn {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              padding: 12px 24px;
              border: none;
              border-radius: 8px;
              font-size: 14px;
              font-weight: 600;
              cursor: pointer;
              transition: all 0.2s ease;
              text-decoration: none;
            }

            .btn-primary {
              background: rgba(255, 255, 255, 0.2);
              color: white;
              border: 2px solid rgba(255, 255, 255, 0.3);
            }

            .btn-primary:hover {
              background: rgba(255, 255, 255, 0.3);
              border-color: rgba(255, 255, 255, 0.5);
              transform: translateY(-2px);
            }

            .btn-secondary {
              background: transparent;
              color: white;
              border: 2px solid rgba(255, 255, 255, 0.3);
            }

            .btn-secondary:hover {
              background: rgba(255, 255, 255, 0.1);
              border-color: rgba(255, 255, 255, 0.5);
            }

            .error-details {
              text-align: left;
              background: rgba(0, 0, 0, 0.2);
              border-radius: 8px;
              padding: 16px;
              margin-top: 24px;
            }

            .error-details summary {
              cursor: pointer;
              font-weight: 600;
              margin-bottom: 16px;
              padding: 8px;
              border-radius: 4px;
              background: rgba(255, 255, 255, 0.1);
            }

            .error-details summary:hover {
              background: rgba(255, 255, 255, 0.2);
            }

            .error-stack {
              font-size: 12px;
            }

            .error-stack h3 {
              font-size: 14px;
              margin: 16px 0 8px 0;
              color: #ffc107;
            }

            .error-stack pre {
              background: rgba(0, 0, 0, 0.3);
              padding: 12px;
              border-radius: 4px;
              overflow-x: auto;
              white-space: pre-wrap;
              word-break: break-word;
              font-family: 'Courier New', monospace;
              font-size: 11px;
              line-height: 1.4;
            }

            @media (max-width: 768px) {
              .error-boundary {
                padding: 16px;
              }

              h1 {
                font-size: 24px;
              }

              .error-message {
                font-size: 14px;
              }

              .btn {
                padding: 10px 20px;
                font-size: 13px;
              }
            }
          `})]}):this.props.children}}function Qw(){const{state:e}=oi();return y.jsxs(Gw,{children:[y.jsxs("div",{className:"app",children:[y.jsx($w,{}),e.showRecordingWindow&&y.jsx("div",{className:"recording-overlay",children:y.jsx(Ww,{})}),e.showSettingsWindow&&y.jsx("div",{className:"settings-overlay",children:y.jsx(Hw,{})}),y.jsx(Kw,{notifications:e.notifications}),e.isLoading&&y.jsx("div",{className:"loading-overlay",children:y.jsxs("div",{className:"loading-content",children:[y.jsx("div",{className:"loading-spinner"}),y.jsx("p",{children:"載入中..."})]})})]}),y.jsx("style",{children:`
        .app {
          width: 100%;
          height: 100%;
          position: relative;
          overflow: hidden;
        }

        .recording-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .settings-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1001;
        }

        .loading-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 9999;
        }

        .loading-content {
          background: white;
          padding: 32px;
          border-radius: 12px;
          text-align: center;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .loading-content p {
          margin-top: 16px;
          color: #666;
          font-size: 14px;
        }
      `})]})}async function jd(){console.log("🚀 Initializing React app..."),typeof window<"u"&&!window.electronAPI&&(console.log("⏳ Loading MockElectronAPI..."),await Am(()=>import("./MockElectronAPI-CWWioucj.js"),[]),console.log("✅ MockElectronAPI loaded"));const e=document.getElementById("root");if(!e){console.error("❌ Root element not found!");return}console.log("✅ Root element found, creating React root..."),Bs.createRoot(e).render(y.jsx(jo.StrictMode,{children:y.jsx(pv,{children:y.jsx(Qw,{})})})),console.log("✅ React app rendered!")}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",jd):jd();document.addEventListener("contextmenu",e=>{e.preventDefault()});document.addEventListener("dragover",e=>{e.preventDefault()});document.addEventListener("drop",e=>{e.preventDefault()});export{nv as D};
