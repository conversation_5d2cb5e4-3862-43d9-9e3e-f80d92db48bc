<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeechPilot Recording</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(0, 0, 0, 0.85);
            color: white;
            border-radius: 12px;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .container {
            padding: 20px;
            text-align: center;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .recording-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .recording-icon.recording {
            background: linear-gradient(45deg, #ff4757, #ff6b7a);
            animation: pulse 1.5s infinite;
        }

        .recording-icon.processing {
            background: linear-gradient(45deg, #3742fa, #5352ed);
            animation: spin 1s linear infinite;
        }

        .recording-icon.completed {
            background: linear-gradient(45deg, #2ed573, #7bed9f);
        }

        .recording-icon.error {
            background: linear-gradient(45deg, #ff4757, #ff3838);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .mode-text {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #ffffff;
        }

        .status-text {
            font-size: 14px;
            color: #cccccc;
            margin-bottom: 8px;
        }

        .duration {
            font-size: 12px;
            color: #999999;
            font-family: 'Courier New', monospace;
        }

        .message {
            font-size: 12px;
            color: #ffcc00;
            margin-top: 8px;
            max-width: 350px;
            word-wrap: break-word;
        }

        .close-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <button class="close-btn" onclick="closeWindow()">×</button>
        
        <div class="recording-icon recording" id="recordingIcon">
            🎤
        </div>
        
        <div class="mode-text" id="modeText">AI 智能模式</div>
        <div class="status-text" id="statusText">正在錄音...</div>
        <div class="duration" id="duration">00:00</div>
        <div class="message" id="message"></div>
    </div>

    <script>
        const { ipcRenderer } = require('electron')
        
        let startTime = Date.now()
        let durationInterval = null

        // 狀態圖標映射
        const statusIcons = {
            recording: '🎤',
            processing: '🧠',
            completed: '✅',
            error: '❌'
        }

        // 狀態文本映射
        const statusTexts = {
            recording: '正在錄音...',
            processing: '正在處理...',
            completed: '處理完成',
            error: '處理失敗'
        }

        // 模式文本映射
        const modeTexts = {
            ai: 'AI 智能模式',
            direct: '直接轉錄模式'
        }

        // 監聽狀態更新
        ipcRenderer.on('status-update', (event, status) => {
            updateUI(status)
        })

        function updateUI(status) {
            const icon = document.getElementById('recordingIcon')
            const modeText = document.getElementById('modeText')
            const statusText = document.getElementById('statusText')
            const message = document.getElementById('message')

            // 更新圖標
            icon.textContent = statusIcons[status.status] || '🎤'
            icon.className = `recording-icon ${status.status}`

            // 更新模式文本
            modeText.textContent = modeTexts[status.mode] || 'AI 智能模式'

            // 更新狀態文本
            statusText.textContent = statusTexts[status.status] || '正在錄音...'

            // 更新消息
            if (status.message) {
                message.textContent = status.message
                message.style.display = 'block'
            } else {
                message.style.display = 'none'
            }

            // 管理計時器 - 直接模式下即使在處理中也要保持計時器運行
            if (status.isRecording && (status.status === 'recording' || status.status === 'processing')) {
                startDurationTimer()
            } else {
                stopDurationTimer()
            }
        }

        function startDurationTimer() {
            if (durationInterval) return
            
            startTime = Date.now()
            durationInterval = setInterval(() => {
                const elapsed = Math.floor((Date.now() - startTime) / 1000)
                const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0')
                const seconds = (elapsed % 60).toString().padStart(2, '0')
                document.getElementById('duration').textContent = `${minutes}:${seconds}`
            }, 1000)
        }

        function stopDurationTimer() {
            if (durationInterval) {
                clearInterval(durationInterval)
                durationInterval = null
            }
        }

        function closeWindow() {
            window.close()
        }

        // 窗口拖拽功能
        let isDragging = false
        let dragOffset = { x: 0, y: 0 }

        document.addEventListener('mousedown', (e) => {
            if (e.target.className !== 'close-btn') {
                isDragging = true
                dragOffset.x = e.clientX
                dragOffset.y = e.clientY
            }
        })

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - dragOffset.x
                const deltaY = e.clientY - dragOffset.y
                ipcRenderer.send('move-window', { deltaX, deltaY })
                dragOffset.x = e.clientX
                dragOffset.y = e.clientY
            }
        })

        document.addEventListener('mouseup', () => {
            isDragging = false
        })
    </script>
</body>
</html>
