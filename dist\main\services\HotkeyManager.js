"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotkeyManager = void 0;
const electron_1 = require("electron");
const events_1 = require("events");
class HotkeyManager extends events_1.EventEmitter {
    constructor() {
        super();
        Object.defineProperty(this, "registeredHotkeys", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
        Object.defineProperty(this, "pressedKeys", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Set()
        });
        Object.defineProperty(this, "pressHandlers", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
        Object.defineProperty(this, "releaseHandlers", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: new Map()
        });
    }
    async registerHotkey(accelerator, onPress, onRelease) {
        try {
            // 如果已經註冊了這個快捷鍵，先取消註冊
            if (this.registeredHotkeys.has(accelerator)) {
                this.unregisterHotkey(accelerator);
            }
            // 註冊快捷鍵
            const success = electron_1.globalShortcut.register(accelerator, () => {
                if (!this.pressedKeys.has(accelerator)) {
                    this.pressedKeys.add(accelerator);
                    onPress();
                    this.emit('hotkey-pressed', accelerator);
                }
            });
            if (success) {
                this.registeredHotkeys.set(accelerator, accelerator);
                this.pressHandlers.set(accelerator, onPress);
                if (onRelease) {
                    this.releaseHandlers.set(accelerator, onRelease);
                }
                console.log(`Hotkey registered: ${accelerator}`);
                return true;
            }
            else {
                console.error(`Failed to register hotkey: ${accelerator}`);
                return false;
            }
        }
        catch (error) {
            console.error(`Error registering hotkey ${accelerator}:`, error);
            return false;
        }
    }
    unregisterHotkey(accelerator) {
        try {
            if (this.registeredHotkeys.has(accelerator)) {
                electron_1.globalShortcut.unregister(accelerator);
                this.registeredHotkeys.delete(accelerator);
                this.pressHandlers.delete(accelerator);
                this.releaseHandlers.delete(accelerator);
                this.pressedKeys.delete(accelerator);
                console.log(`Hotkey unregistered: ${accelerator}`);
                this.emit('hotkey-unregistered', accelerator);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Error unregistering hotkey ${accelerator}:`, error);
            return false;
        }
    }
    unregisterAllHotkeys() {
        try {
            electron_1.globalShortcut.unregisterAll();
            this.registeredHotkeys.clear();
            this.pressHandlers.clear();
            this.releaseHandlers.clear();
            this.pressedKeys.clear();
            console.log('All hotkeys unregistered');
            this.emit('all-hotkeys-unregistered');
        }
        catch (error) {
            console.error('Error unregistering all hotkeys:', error);
        }
    }
    isHotkeyRegistered(accelerator) {
        return this.registeredHotkeys.has(accelerator);
    }
    getRegisteredHotkeys() {
        return Array.from(this.registeredHotkeys.keys());
    }
    // 檢查快捷鍵是否可用
    isHotkeyAvailable(accelerator) {
        try {
            // 嘗試註冊快捷鍵
            const success = electron_1.globalShortcut.register(accelerator, () => { });
            if (success) {
                // 立即取消註冊
                electron_1.globalShortcut.unregister(accelerator);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Error checking hotkey availability ${accelerator}:`, error);
            return false;
        }
    }
    // 驗證快捷鍵格式
    validateHotkey(accelerator) {
        try {
            // 檢查基本格式
            if (!accelerator || typeof accelerator !== 'string') {
                return false;
            }
            // 檢查是否包含有效的修飾鍵
            const validModifiers = [
                'CommandOrControl', 'Command', 'Control', 'Ctrl',
                'Alt', 'Option', 'AltGr', 'Shift', 'Super', 'Meta'
            ];
            const parts = accelerator.split('+');
            if (parts.length < 2) {
                return false;
            }
            const modifiers = parts.slice(0, -1);
            const key = parts[parts.length - 1];
            // 檢查修飾鍵是否有效
            const hasValidModifier = modifiers.some(mod => validModifiers.includes(mod));
            // 檢查主鍵是否有效
            const hasValidKey = key && key.length > 0;
            return hasValidModifier && !!hasValidKey;
        }
        catch (error) {
            console.error(`Error validating hotkey ${accelerator}:`, error);
            return false;
        }
    }
    // 標準化快捷鍵格式
    normalizeHotkey(accelerator) {
        try {
            const parts = accelerator.split('+').map(part => part.trim());
            // 標準化修飾鍵
            const normalizedParts = parts.map(part => {
                switch (part.toLowerCase()) {
                    case 'ctrl':
                    case 'control':
                        return 'CommandOrControl';
                    case 'cmd':
                    case 'command':
                        return 'Command';
                    case 'alt':
                    case 'option':
                        return 'Alt';
                    case 'shift':
                        return 'Shift';
                    case 'super':
                    case 'meta':
                        return 'Super';
                    default:
                        return part;
                }
            });
            return normalizedParts.join('+');
        }
        catch (error) {
            console.error(`Error normalizing hotkey ${accelerator}:`, error);
            return accelerator;
        }
    }
    // 獲取快捷鍵衝突
    getHotkeyConflicts(accelerator) {
        const conflicts = [];
        // 檢查是否與已註冊的快捷鍵衝突
        if (this.registeredHotkeys.has(accelerator)) {
            conflicts.push('Already registered in SpeechPilot');
        }
        // 檢查是否與系統快捷鍵衝突
        if (!this.isHotkeyAvailable(accelerator)) {
            conflicts.push('Conflicts with system hotkey');
        }
        return conflicts;
    }
    // 建議替代快捷鍵
    suggestAlternativeHotkeys(baseAccelerator) {
        const suggestions = [];
        const parts = baseAccelerator.split('+');
        if (parts.length < 2) {
            return suggestions;
        }
        const modifiers = parts.slice(0, -1);
        const key = parts[parts.length - 1];
        // 嘗試不同的修飾鍵組合
        const alternativeModifiers = [
            ['CommandOrControl', 'Shift'],
            ['CommandOrControl', 'Alt'],
            ['CommandOrControl', 'Shift', 'Alt'],
            ['Alt', 'Shift']
        ];
        for (const altModifiers of alternativeModifiers) {
            const suggestion = [...altModifiers, key].join('+');
            if (this.isHotkeyAvailable(suggestion) && !suggestions.includes(suggestion)) {
                suggestions.push(suggestion);
            }
        }
        // 嘗試不同的按鍵
        const alternativeKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
        for (const altKey of alternativeKeys) {
            const suggestion = [...modifiers, altKey].join('+');
            if (this.isHotkeyAvailable(suggestion) && !suggestions.includes(suggestion)) {
                suggestions.push(suggestion);
            }
        }
        return suggestions.slice(0, 5); // 返回最多 5 個建議
    }
    // 模擬按鍵釋放（用於 press 模式）
    simulateKeyRelease(accelerator) {
        if (this.pressedKeys.has(accelerator)) {
            this.pressedKeys.delete(accelerator);
            const releaseHandler = this.releaseHandlers.get(accelerator);
            if (releaseHandler) {
                releaseHandler();
            }
            this.emit('hotkey-released', accelerator);
        }
    }
    // 清理資源
    cleanup() {
        this.unregisterAllHotkeys();
        this.removeAllListeners();
    }
}
exports.HotkeyManager = HotkeyManager;
