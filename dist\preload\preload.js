"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// 創建安全的 IPC 包裝器
const createIPCWrapper = () => {
    const invoke = async (channel, data) => {
        return electron_1.ipcRenderer.invoke(channel, data);
    };
    const on = (channel, callback) => {
        const listener = (_, data) => callback(data);
        electron_1.ipcRenderer.on(channel, listener);
        // 返回取消監聽的函數
        return () => {
            electron_1.ipcRenderer.removeListener(channel, listener);
        };
    };
    return { invoke, on };
};
const { invoke, on } = createIPCWrapper();
// 實現 ElectronAPI
const electronAPI = {
    // 配置管理
    getConfig: () => invoke('get-config'),
    updateConfig: (config) => invoke('update-config', config),
    // 錄音控制
    startRecording: (mode) => invoke('start-recording', { mode }),
    stopRecording: () => invoke('stop-recording'),
    getAudioDevices: () => invoke('get-audio-devices'),
    // 窗口控制
    showSettings: () => invoke('show-settings'),
    hideSettings: () => invoke('hide-settings'),
    showRecordingWindow: () => invoke('show-recording-window'),
    hideRecordingWindow: () => invoke('hide-recording-window'),
    minimizeToTray: () => invoke('minimize-to-tray'),
    quitApp: () => invoke('quit-app'),
    // 事件監聽
    onConfigUpdated: (callback) => on('config-updated', callback),
    onRecordingStateChanged: (callback) => on('recording-state-changed', callback),
    onHotkeyTriggered: (callback) => on('hotkey-triggered', callback),
    onProcessingResult: (callback) => on('processing-result', callback),
    onErrorOccurred: (callback) => on('error-occurred', callback),
    // 系統信息
    platform: process.platform,
    version: process.versions.electron
};
// 暴露 API 到渲染進程
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
