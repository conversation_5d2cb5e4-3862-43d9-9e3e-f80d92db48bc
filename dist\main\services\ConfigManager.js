"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const electron_store_1 = __importDefault(require("electron-store"));
const events_1 = require("events");
const constants_1 = require("../../shared/constants");
class ConfigManager extends events_1.EventEmitter {
    constructor() {
        super();
        Object.defineProperty(this, "store", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "config", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        // 從環境變數創建初始配置
        const envConfig = this.loadConfigFromEnv();
        // 初始化 electron-store
        this.store = new electron_store_1.default({
            defaults: envConfig,
            name: 'speechpilot-config',
            encryptionKey: 'speechpilot-encryption-key', // 加密敏感數據
            schema: {
                azureOpenAI: {
                    type: 'object',
                    properties: {
                        endpoint: { type: 'string' },
                        apiKey: { type: 'string' },
                        model: { type: 'string' }
                    },
                    required: ['endpoint', 'apiKey', 'model']
                },
                azureSpeech: {
                    type: 'object',
                    properties: {
                        region: { type: 'string' },
                        apiKey: { type: 'string' }
                    },
                    required: ['region', 'apiKey']
                },
                hotkeys: {
                    type: 'object',
                    properties: {
                        aiMode: { type: 'string' },
                        directMode: { type: 'string' }
                    }
                },
                recording: {
                    type: 'object',
                    properties: {
                        deviceId: { type: 'string' },
                        hotkeyMode: { type: 'string', enum: ['press', 'toggle'] }
                    }
                },
                ui: {
                    type: 'object',
                    properties: {
                        theme: { type: 'string', enum: ['light', 'dark'] },
                        language: { type: 'string', enum: ['zh-TW', 'en-US'] }
                    }
                }
            }
        });
        this.config = this.store.store;
    }
    loadConfigFromEnv() {
        console.log('Loading config from environment variables...');
        const config = { ...constants_1.DEFAULT_CONFIG };
        // 載入 Azure OpenAI 配置
        if (process.env.AZURE_OPENAI_ENDPOINT) {
            config.azureOpenAI.endpoint = process.env.AZURE_OPENAI_ENDPOINT;
        }
        if (process.env.AZURE_OPENAI_API_KEY) {
            config.azureOpenAI.apiKey = process.env.AZURE_OPENAI_API_KEY;
        }
        if (process.env.AZURE_OPENAI_MODEL) {
            config.azureOpenAI.model = process.env.AZURE_OPENAI_MODEL;
        }
        if (process.env.AZURE_OPENAI_TRANSCRIBE_MODEL) {
            config.azureOpenAI.transcribeModel = process.env.AZURE_OPENAI_TRANSCRIBE_MODEL;
        }
        // 載入 Azure Speech 配置
        if (process.env.AZURE_SPEECH_SERVICE_REGION) {
            config.azureSpeech.region = process.env.AZURE_SPEECH_SERVICE_REGION;
        }
        if (process.env.AZURE_SPEECH_SERVICE_API_KEY) {
            config.azureSpeech.apiKey = process.env.AZURE_SPEECH_SERVICE_API_KEY;
        }
        console.log('Environment config loaded:', {
            hasOpenAIKey: !!config.azureOpenAI.apiKey,
            hasOpenAIEndpoint: !!config.azureOpenAI.endpoint,
            hasSpeechKey: !!config.azureSpeech.apiKey,
            speechRegion: config.azureSpeech.region
        });
        return config;
    }
    async initialize() {
        try {
            // 從環境變數載入最新配置
            const envConfig = this.loadConfigFromEnv();
            // 獲取已保存的配置
            const savedConfig = this.store.store;
            // 合併配置：環境變數優先，但保留用戶設置
            this.config = this.deepMerge(savedConfig, envConfig);
            // 更新 store 以確保環境變數的值被保存
            this.store.store = this.config;
            // 驗證配置
            this.validateConfig();
            // 監聽配置變更
            this.store.onDidAnyChange((newValue) => {
                if (newValue) {
                    this.config = newValue;
                    this.emit('config-changed', this.config);
                }
            });
            console.log('ConfigManager initialized with merged config');
            console.log('Final config validation:', {
                hasOpenAIKey: !!this.config.azureOpenAI.apiKey,
                hasOpenAIEndpoint: !!this.config.azureOpenAI.endpoint,
                hasSpeechKey: !!this.config.azureSpeech.apiKey,
                speechRegion: this.config.azureSpeech.region
            });
        }
        catch (error) {
            console.error('Failed to initialize ConfigManager:', error);
            throw error;
        }
    }
    getConfig() {
        return { ...this.config };
    }
    async updateConfig(updates) {
        try {
            // 深度合併配置
            const newConfig = this.deepMerge(this.config, updates);
            // 驗證新配置
            this.validatePartialConfig(updates);
            // 保存配置
            this.store.store = newConfig;
            this.config = newConfig;
            // 發出變更事件
            this.emit('config-changed', this.config);
            console.log('Config updated:', updates);
        }
        catch (error) {
            console.error('Failed to update config:', error);
            throw error;
        }
    }
    // 獲取特定配置項
    get(key) {
        return this.config[key];
    }
    // 設置特定配置項
    async set(key, value) {
        await this.updateConfig({ [key]: value });
    }
    // 重置配置
    async resetConfig() {
        this.store.clear();
        this.config = constants_1.DEFAULT_CONFIG;
        this.emit('config-changed', this.config);
    }
    // 檢查 API 金鑰是否已設置
    hasValidAPIKeys() {
        return !!(this.config.azureOpenAI.apiKey &&
            this.config.azureOpenAI.endpoint &&
            this.config.azureSpeech.apiKey &&
            this.config.azureSpeech.region);
    }
    // 獲取配置文件路徑
    getConfigPath() {
        return this.store.path;
    }
    validateConfig() {
        if (!this.config) {
            throw new Error('Config is null or undefined');
        }
        // 檢查必要的配置結構
        const requiredKeys = ['azureOpenAI', 'azureSpeech', 'hotkeys', 'recording', 'ui'];
        for (const key of requiredKeys) {
            if (!(key in this.config)) {
                console.warn(`Missing config key: ${key}, using default`);
                this.config = { ...constants_1.DEFAULT_CONFIG, ...this.config };
                break;
            }
        }
    }
    validatePartialConfig(updates) {
        // 驗證 API 金鑰格式
        if (updates.azureOpenAI?.apiKey && !this.isValidAPIKey(updates.azureOpenAI.apiKey)) {
            throw new Error('Invalid Azure OpenAI API key format');
        }
        if (updates.azureSpeech?.apiKey && !this.isValidAPIKey(updates.azureSpeech.apiKey)) {
            throw new Error('Invalid Azure Speech API key format');
        }
        // 驗證快捷鍵格式
        if (updates.hotkeys) {
            for (const [key, hotkey] of Object.entries(updates.hotkeys)) {
                if (hotkey && !this.isValidHotkey(hotkey)) {
                    throw new Error(`Invalid hotkey format for ${key}: ${hotkey}`);
                }
            }
        }
    }
    isValidAPIKey(apiKey) {
        // Azure API 金鑰通常是 32 個字符的字母數字字符串
        return /^[a-zA-Z0-9]{32,}$/.test(apiKey);
    }
    isValidHotkey(hotkey) {
        // 簡單的快捷鍵格式驗證
        const validModifiers = ['CommandOrControl', 'Command', 'Control', 'Alt', 'Shift', 'Super'];
        const parts = hotkey.split('+');
        if (parts.length < 2)
            return false;
        const modifiers = parts.slice(0, -1);
        const key = parts[parts.length - 1];
        return modifiers.every(mod => validModifiers.includes(mod)) && key.length > 0;
    }
    deepMerge(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(target[key] || {}, source[key]);
            }
            else {
                result[key] = source[key];
            }
        }
        return result;
    }
}
exports.ConfigManager = ConfigManager;
