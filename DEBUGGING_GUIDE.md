# 🔍 SpeechPilot 調試指南

## 🎯 已完成的修復

### 1. 添加詳細日誌記錄
- ✅ **MockElectronAPI**: 添加了詳細的日誌記錄，包含 emoji 標識
- ✅ **AppContext**: 添加了狀態變化和錄音流程的日誌
- ✅ **RecordingWindow**: 添加了組件渲染和狀態的日誌

### 2. 修復錄音窗口顯示問題
- ✅ **窗口大小**: 修復了 RecordingWindow 的尺寸問題（400x300px）
- ✅ **樣式優化**: 添加了圓角、陰影和適當的 overflow 處理
- ✅ **模式顯示**: 更新了處理文字以顯示 gpt-4o-mini-transcribe

### 3. 專注於直接轉錄模式
- ✅ **模型更新**: 使用 gpt-4o-mini-transcribe 進行直接語音轉錄
- ✅ **模擬結果**: 更新了模擬 API 以反映正確的模式和結果
- ✅ **配置支援**: 添加了 transcribeModel 配置選項

## 🧪 測試步驟

### 瀏覽器測試 (http://localhost:3000)

1. **打開開發者工具**
   - 按 F12 打開控制台
   - 查看日誌輸出

2. **測試直接轉錄模式**
   - 點擊「直接語音輸入」按鈕
   - 觀察控制台日誌：
     ```
     🎙️ AppContext: Starting recording in direct mode
     🎙️ AppContext: Updating recording state
     🎙️ AppContext: Showing recording window
     🎙️ RecordingWindow: Component rendering
     ```

3. **檢查錄音窗口**
   - 應該看到一個 400x300px 的白色窗口
   - 窗口頂部顯示「直接轉錄模式」
   - 中央有紅色的麥克風圖標
   - 顯示「正在錄音...」文字

4. **測試停止錄音**
   - 點擊「停止錄音」按鈕或按 Space/Enter
   - 觀察處理過程：
     ```
     🛑 MockElectronAPI: Stopping recording
     ⚙️ MockElectronAPI: Starting processing simulation
     📝 MockElectronAPI: Using direct transcription mode
     ```

5. **檢查結果**
   - 應該顯示「正在使用 gpt-4o-mini-transcribe 轉錄中...」
   - 完成後顯示模擬的轉錄結果
   - 文字會自動複製到剪貼簿

## 🔧 調試信息

### 控制台日誌標識
- 🎙️ 錄音相關操作
- 🛑 停止錄音操作
- ⚙️ 處理過程
- 📝 直接轉錄模式
- 🤖 AI 智能模式
- 📋 結果生成
- ⌨️ 文字輸入
- ✅ 成功操作
- ❌ 錯誤操作
- 🔄 狀態變化
- 🚪 窗口操作

### 常見問題排查

1. **錄音窗口不顯示**
   - 檢查控制台是否有錯誤
   - 確認 `showRecordingWindow` 狀態為 true
   - 檢查 CSS 樣式是否正確載入

2. **白屏問題**
   - 檢查 RecordingWindow 組件是否正確渲染
   - 確認 recordingState 不為 null
   - 檢查 motion.div 是否正確動畫

3. **模式不正確**
   - 確認 lastRecordingMode 正確設置
   - 檢查按鈕點擊事件是否傳遞正確的模式

## 📋 下一步調試

如果問題仍然存在：

1. **檢查網路請求**
   - 打開 Network 標籤
   - 查看是否有失敗的請求

2. **檢查 React 組件樹**
   - 使用 React Developer Tools
   - 檢查組件狀態和 props

3. **檢查 CSS 樣式**
   - 使用 Elements 標籤
   - 檢查 .recording-overlay 和 .recording-window 樣式

4. **檢查 JavaScript 錯誤**
   - 查看控制台的紅色錯誤信息
   - 檢查是否有未捕獲的異常

## 🎯 預期行為

### 正常流程
1. 點擊「直接語音輸入」→ 顯示錄音窗口
2. 窗口顯示紅色麥克風圖標和「正在錄音...」
3. 點擊停止 → 顯示黃色載入圖標和「正在使用 gpt-4o-mini-transcribe 轉錄中...」
4. 處理完成 → 顯示綠色勾選圖標和轉錄結果
5. 3秒後自動關閉窗口

### 日誌輸出示例
```
🎙️ AppContext: Starting recording in direct mode
🎙️ MockElectronAPI: Starting recording in direct mode
🎙️ MockElectronAPI: Emitting recording-state-changed
🔄 AppContext: Recording state changed: {isRecording: true, mode: "direct", status: "recording"}
🎙️ RecordingWindow: Component rendering
🛑 MockElectronAPI: Stopping recording
⚙️ MockElectronAPI: Starting processing simulation
📝 MockElectronAPI: Using direct transcription mode
📋 MockElectronAPI: Generated result: 這是模擬的直接語音轉錄結果...
⌨️ MockElectronAPI: Inputting text to clipboard
✅ MockElectronAPI: Emitting completed state
🚪 MockElectronAPI: Hiding recording window
```

現在請測試更新後的功能，並查看控制台日誌來幫助診斷任何問題！
